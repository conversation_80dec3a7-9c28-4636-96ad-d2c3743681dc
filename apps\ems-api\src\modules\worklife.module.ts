import { Module } from '@nestjs/common';
import { HttpModule } from 'packages/http';
import { WorklifeController } from '../controllers/worklife.controller';
import { WorklifeService } from '../providers/services/worklife.service';

@Module({
  // -- not used
  imports: [
    HttpModule.register({
      baseURL: process.env.EMS_API_URL,
      apiTenant: 'ems',
      authTokenParams: {
        oracleCustomToken: true,
      },
    }),
  ],
  controllers: [WorklifeController],
  providers: [WorklifeService],
})
export class WorklifeModule {}
