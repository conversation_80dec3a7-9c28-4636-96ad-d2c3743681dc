import { Module } from '@nestjs/common';
import { HttpModule } from 'packages/http';
import { GradeController } from '../controllers/grade.controller';
import { GradeService } from '../providers/services/grade.service';

@Module({
  // -- not used
  imports: [
    HttpModule.register({
      baseURL: process.env.EMS_API_URL,
      apiTenant: 'ems',
      authTokenParams: {
        oracleCustomToken: true,
      },
    }),
  ],
  controllers: [GradeController],
  providers: [GradeService],
})
export class GradeModule {}
