import { Module } from '@nestjs/common';
import { HttpModule } from 'packages/http';
import { StaffingController } from '../controllers/staffing.controller';
import { StaffingService } from '../providers/services/staffing.service';

@Module({
  // -- not used
  imports: [
    HttpModule.register({
      baseURL: process.env.EMS_API_URL,
      apiTenant: 'ems',
      authTokenParams: {
        oracleCustomToken: true,
      },
    }),
  ],
  controllers: [StaffingController],
  providers: [StaffingService],
})
export class StaffingModule {}
