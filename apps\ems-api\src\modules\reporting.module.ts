import { Module } from '@nestjs/common';
import { HttpModule } from 'packages/http';
import { ReportingController } from '../controllers/reporting.controller';
import { ReportingService } from '../providers/services/reporting.service';

@Module({
  // -- not used
  imports: [
    HttpModule.register({
      baseURL: process.env.EMS_API_URL,
      apiTenant: 'ems',
      authTokenParams: {
        oracleCustomToken: true,
      },
    }),
  ],
  controllers: [ReportingController],
  providers: [ReportingService],
})
export class ReportingModule {}
