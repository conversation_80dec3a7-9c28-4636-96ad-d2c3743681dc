import { INestApplication, ValidationPipe } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { json, urlencoded } from 'express';
import * as session from 'express-session';
import { CustomExceptionFilter } from 'packages/exception';
import { GlobalModule } from './global.module';
async function bootstrap() {
  // const app = await NestFactory.create<NestFastifyApplication>(
  //   AppModule,
  //   new FastifyAdapter({ logger: true })
  // );
  const app = await NestFactory.create<INestApplication>(GlobalModule, {
    snapshot: true,
    abortOnError: false,
  });
  app.use(json({ limit: '50mb' }));
  app.use(urlencoded({ extended: true, limit: '50mb' }));
  //app.useGlobalInterceptors(app.get(ExternalAuthInterceptor));
  app.useGlobalPipes(
    new ValidationPipe({
      disableErrorMessages: false,
      enableDebugMessages: true,
      skipMissingProperties: true,
      skipUndefinedProperties: true,
    }),
  );
  app.use(
    session({
      secret: '600d414e-282e-4744-92b1-44869f09aa33',
      resave: true,
      saveUninitialized: true,
    }),
  );
  app.enableCors({
    origin: '*',
    allowedHeaders: '*',
    exposedHeaders: ['AccessToken', 'RefreshToken'],
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
    credentials: true,
    preflightContinue: false,
    optionsSuccessStatus: 204,
  });

  const config = new DocumentBuilder()
    .setTitle('Employee API')
    .setDescription('The EMS and SSR API description')
    .setVersion('1.0')
    .build();
  const documentFactory = () => SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api', app, documentFactory, {
    jsonDocumentUrl: 'swagger/json',
  });
  app.useGlobalFilters(new CustomExceptionFilter());
  // const httpAdapter = app.get(HttpAdapterHost);
  // app.useGlobalFilters(new CustomExceptionFilter());
  // app.useGlobalFilters(new CatchEverythingFilter(httpAdapter));
  console.log('using port 8080');
  await app.listen(process.env.PORT || 8080);
}
bootstrap();
