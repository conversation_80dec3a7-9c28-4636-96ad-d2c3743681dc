import { Module } from '@nestjs/common';
import { HttpModule } from 'packages/http';
import { CompetitionController } from '../controllers/competition.controller';
import { CompetitionService } from '../providers/services/competition.service';

@Module({
  // -- not used
  imports: [
    HttpModule.register({
      baseURL: process.env.EMS_API_URL,
      apiTenant: 'ems',
      authTokenParams: {
        oracleCustomToken: true,
      },
    }),
  ],
  controllers: [CompetitionController],
  providers: [CompetitionService],
})
export class CompetitionModule {}
