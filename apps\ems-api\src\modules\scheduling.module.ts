import { Module } from '@nestjs/common';
import { HttpModule } from 'packages/http';
import { SchedulingController } from '../controllers/scheduling.controller';
import { SchedulingService } from '../providers/services/scheduling.service';

@Module({
  // -- not used
  imports: [
    HttpModule.register({
      baseURL: process.env.EMS_API_URL,
      apiTenant: 'ems',
      authTokenParams: {
        oracleCustomToken: true,
      },
    }),
  ],
  controllers: [SchedulingController],
  providers: [SchedulingService],
})
export class SchedulingModule {}
