import { Module } from '@nestjs/common';
import { HttpModule } from 'packages/http';
import { BcController } from '../controllers/bc.controller';
import { BcService } from '../providers/services/bc.service';

@Module({
  // -- not used
  imports: [
    HttpModule.register({
      baseURL: process.env.EMS_API_URL,
      apiTenant: 'ems',
      authTokenParams: {
        oracleCustomToken: true,
      },
    }),
  ],
  controllers: [BcController],
  providers: [BcService],
})
export class BcModule {}
