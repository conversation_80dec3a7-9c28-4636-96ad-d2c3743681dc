import { Module } from '@nestjs/common';
import { HttpModule } from 'packages/http';
import { BenefitController } from '../controllers/benefit.controller';
import { BenefitService } from '../providers/services/benefit.service';

@Module({
  // -- not used
  imports: [
    HttpModule.register({
      baseURL: process.env.EMS_API_URL,
      apiTenant: 'ems',
      authTokenParams: {
        oracleCustomToken: true,
      },
    }),
  ],
  controllers: [BenefitController],
  providers: [BenefitService],
})
export class BenefitModule {}
