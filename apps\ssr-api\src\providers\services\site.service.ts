import { Injectable } from '@nestjs/common';
import { HttpService } from 'packages/http';
import { BusinessCentralCustomerModel } from '../models/site/businessCentralCustomer.model';
import { SiteModel } from '../models/site/site.model';
@Injectable()
export class SiteService {
  constructor(private readonly http: HttpService) {}

  public async getBusinessCentral(): Promise<BusinessCentralCustomerModel> {
    // -- not used
    const response = await this.http.get<BusinessCentralCustomerModel>(
      `IntegrationData/Get/BusinessCentral`,
      {},
      false,
      { baseURL: 'https://apilooppe.sn.ac.ae/gateway/Parent' }
    );
    return response;
  }

  public async getSite(
    activeinactive?: string,
    advanced_search?: string,
    azuresites?: boolean,
    client_id?: number,
    contract_id?: number,
    count?: number,
    exclude_internal?: boolean,
    gfisites?: boolean,
    idonly?: boolean,
    includeactive?: boolean,
    includeaddress?: boolean,
    includeinactive?: boolean,
    includenonstocklocations?: boolean,
    includenoorderstockbin?: boolean,
    includenotes?: boolean,
    includestocklocations?: boolean,
    include_custom_fields?: string,
    iscalendarfilter?: boolean,
    item_id_qty?: number,
    item_salesorder_id?: number,
    item_salesorder_line?: number,
    lastupdatefromdate?: boolean,
    lastupdatetodate?: boolean,
    order?: string,
    order2?: string,
    order3?: string,
    order4?: string,
    order5?: string,
    orderdesc?: boolean,
    orderdesc2?: boolean,
    orderdesc3?: boolean,
    orderdesc4?: boolean,
    orderdesc5?: boolean,
    override_enablestockbins?: boolean,
    page_no?: number,
    page_size?: number,
    pageinate?: boolean,
    search?: string,
    site_id?: number,
    sitefields?: string,
    stocklocation?: string,
    toplevel_id?: number,
    user_override?: number,
  ): Promise<SiteModel> {
    // -- not used
    const response = await this.http.get<SiteModel>(
      `Site`,
      {
        activeinactive: activeinactive,
        advanced_search: advanced_search,
        azuresites: azuresites,
        client_id: client_id,
        contract_id: contract_id,
        count: count,
        exclude_internal: exclude_internal,
        gfisites: gfisites,
        idonly: idonly,
        includeactive: includeactive,
        includeaddress: includeaddress,
        includeinactive: includeinactive,
        includenonstocklocations: includenonstocklocations,
        includenoorderstockbin: includenoorderstockbin,
        includenotes: includenotes,
        includestocklocations: includestocklocations,
        include_custom_fields: include_custom_fields,
        iscalendarfilter: iscalendarfilter,
        item_id_qty: item_id_qty,
        item_salesorder_id: item_salesorder_id,
        item_salesorder_line: item_salesorder_line,
        lastupdatefromdate: lastupdatefromdate,
        lastupdatetodate: lastupdatetodate,
        order: order,
        order2: order2,
        order3: order3,
        order4: order4,
        order5: order5,
        orderdesc: orderdesc,
        orderdesc2: orderdesc2,
        orderdesc3: orderdesc3,
        orderdesc4: orderdesc4,
        orderdesc5: orderdesc5,
        override_enablestockbins: override_enablestockbins,
        page_no: page_no,
        page_size: page_size,
        pageinate: pageinate,
        search: search,
        site_id: site_id,
        sitefields: sitefields,
        stocklocation: stocklocation,
        toplevel_id: toplevel_id,
        user_override: user_override,
      },
      false,
      { baseURL: 'https://apilooppe.sn.ac.ae/gateway/Parent' }
    );
    return response;
  }

  public async postSite(model: SiteModel): Promise<SiteModel> {
    // -- not used
    const response = await this.http.post<SiteModel>(`Site`, model, { baseURL: 'https://apilooppe.sn.ac.ae/gateway/Parent' });
    return response;
  }

  public async getSiteById(
    id: number,
    client_override?: number,
    domain?: string,
    includeactivity?: boolean,
    includedetails?: boolean,
    issetup?: boolean,
    tickettype_id?: number,
  ): Promise<SiteModel> {
    // -- not used
    const response = await this.http.get<SiteModel>(
      `Site/${id}`,
      {
        id: id,
        client_override: client_override,
        domain: domain,
        includeactivity: includeactivity,
        includedetails: includedetails,
        issetup: issetup,
        tickettype_id: tickettype_id,
      },
      false,
      { baseURL: 'https://apilooppe.sn.ac.ae/gateway/Parent' }
    );
    return response;
  }
}
