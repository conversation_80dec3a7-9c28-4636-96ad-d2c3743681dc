import { Module } from '@nestjs/common';
import { HttpModule } from 'packages/http';
import { SuccessionController } from '../controllers/succession.controller';
import { SuccessionService } from '../providers/services/succession.service';

@Module({
  // -- not used
  imports: [
    HttpModule.register({
      baseURL: process.env.EMS_API_URL,
      apiTenant: 'ems',
      authTokenParams: {
        oracleCustomToken: true,
      },
    }),
  ],
  controllers: [SuccessionController],
  providers: [SuccessionService],
})
export class SuccessionModule {}
