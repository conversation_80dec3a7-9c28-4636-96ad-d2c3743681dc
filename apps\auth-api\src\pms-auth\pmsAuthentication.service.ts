/* eslint-disable prettier/prettier */
import { Injectable } from '@nestjs/common';
import { HttpService } from 'packages/http';
import { AuthenticateViewModelModel } from './authentication/authenticateViewModel.model';
import { DeleteSpouseViewModelModel } from './authentication/deleteSpouseViewModel.model';
import { OtpByPassViewModelModel } from './authentication/otpByPassViewModel.model';
import { ParentSignupViewModelModel } from './authentication/parentSignupViewModel.model';
import { VerifySpouseMobileDataModel } from './authentication/verifySpouseMobileData.model';
@Injectable()
export class AuthenticationService {
  constructor(private readonly http: HttpService) {}

  public async testNotification2(): Promise<AuthenticateViewModelModel> {
    // -- not used
    const response = await this.http.get<AuthenticateViewModelModel>(
      `api/authenticate/TestNotification2`,
      {},
      false,
    );
    return response;
  }

  public async testNotification(
    model: AuthenticateViewModelModel,
  ): Promise<AuthenticateViewModelModel> {
    // -- not used
    const response = await this.http.post<AuthenticateViewModelModel>(
      `api/authenticate/TestNotification`,
      model,
    );
    return response;
  }

  public async authenticateParentViaPassword(
    model: AuthenticateViewModelModel,
  ): Promise<AuthenticateViewModelModel> {
    // -- not used
    const response = await this.http.post<AuthenticateViewModelModel>(
      `api/authenticate/AuthenticateParentViaPassword`,
      model,
    );
    return response;
  }

  public async logOutParent(): Promise<AuthenticateViewModelModel> {
    // -- not used
    const response = await this.http.get<AuthenticateViewModelModel>(
      `api/authenticate/LogOutParent`,
      {},
      false,
    );
    return response;
  }

  public async sendParentOTP(
    phoneNumber?: string,
  ): Promise<OtpByPassViewModelModel> {
    // -- not used
    const response = await this.http.get<OtpByPassViewModelModel>(
      `api/authenticate/SendParentOTP`,
      { PhoneNumber: phoneNumber },
      false,
    );
    return response;
  }

  public async sendParentEmailOTP(
    emailId?: string,
  ): Promise<OtpByPassViewModelModel> {
    // -- not used
    const response = await this.http.get<OtpByPassViewModelModel>(
      `api/authenticate/SendParentEmailOTP`,
      { emailId: emailId },
      false,
    );
    return response;
  }

  public async authenticateParentViaOTP(
    model: OtpByPassViewModelModel,
  ): Promise<OtpByPassViewModelModel> {
    // -- not used
    const response = await this.http.post<OtpByPassViewModelModel>(
      `api/authenticate/AuthenticateParentViaOTP`,
      model,
    );
    return response;
  }

  public async verifyParentNumber(
    requestId?: string,
    oTP?: string,
  ): Promise<VerifySpouseMobileDataModel> {
    // -- not used
    const response = await this.http.get<VerifySpouseMobileDataModel>(
      `api/authenticate/VerifyParentNumber`,
      { requestId: requestId, OTP: oTP },
      false,
    );
    return response;
  }

  public async authenticateParentViaEmailOTP(
    emailId?: string,
    oTP?: string,
  ): Promise<AuthenticateViewModelModel> {
    // -- not used
    const response = await this.http.get<AuthenticateViewModelModel>(
      `api/authenticate/AuthenticateParentViaEmailOTP`,
      { emailId: emailId, OTP: oTP },
      false,
    );
    return response;
  }

  public async sendParentMobileOTP(
    phoneNumber?: string,
  ): Promise<OtpByPassViewModelModel> {
    // -- not used
    const response = await this.http.get<OtpByPassViewModelModel>(
      `api/authenticate/SendParentMobileOTP`,
      { PhoneNumber: phoneNumber },
      false,
    );
    return response;
  }

  public async verifyParentMobileOTP(
    phoneNumber?: string,
    otp?: string,
    requestId?: string,
  ): Promise<OtpByPassViewModelModel> {
    // -- not used
    const response = await this.http.get<OtpByPassViewModelModel>(
      `api/authenticate/VerifyParentMobileOTP`,
      { PhoneNumber: phoneNumber, otp: otp, requestId: requestId },
      false,
    );
    return response;
  }

  public async createSpouseMobileOTP(
    phoneNumber?: string,
  ): Promise<OtpByPassViewModelModel> {
    // -- not used
    const response = await this.http.get<OtpByPassViewModelModel>(
      `api/authenticate/CreateSpouseMobileOTP`,
      { PhoneNumber: phoneNumber },
      false,
    );
    return response;
  }

  public async verifyCreateSpouseMobileOTP(
    model: VerifySpouseMobileDataModel,
  ): Promise<VerifySpouseMobileDataModel> {
    // -- not used
    const response = await this.http.post<VerifySpouseMobileDataModel>(
      `api/authenticate/VerifyCreateSpouseMobileOTP`,
      model,
    );
    return response;
  }

  public async sendParentMobileOTPByPass(
    phoneNumber?: string,
  ): Promise<OtpByPassViewModelModel> {
    // -- not used
    const response = await this.http.get<OtpByPassViewModelModel>(
      `authenticate/SendParentMobileOTPByPass`,
      { PhoneNumber: phoneNumber },
      false,
      { baseURL: 'https://apilooppe.sn.ac.ae/gateway/Parent' }
    );
    return response;
  }

  public async verifyParentMobileOTPByPass(
    phoneNumber?: string,
    otp?: string,
    requestId?: string,
  ): Promise<OtpByPassViewModelModel> {
    // -- not used
    const response = await this.http.get<OtpByPassViewModelModel>(
      `api/authenticate/VerifyParentMobileOTPByPass`,
      { PhoneNumber: phoneNumber, otp: otp, requestId: requestId },
      false,
    );
    return response;
  }

  public async sendParentEmailOTPByPass(
    email?: string,
  ): Promise<OtpByPassViewModelModel> {
    // -- not used
    const response = await this.http.get<OtpByPassViewModelModel>(
      `api/authenticate/SendParentEmailOTPByPass`,
      { email: email },
      false,
    );
    return response;
  }

  public async verifyParentEmailOTPByPass(
    email?: string,
    otp?: string,
  ): Promise<OtpByPassViewModelModel> {
    // -- not used
    const response = await this.http.get<OtpByPassViewModelModel>(
      `api/authenticate/VerifyParentEmailOTPByPass`,
      { email: email, otp: otp },
      false,
    );
    return response;
  }

  public async authenticateParentViaOTPByPass(
    model: OtpByPassViewModelModel,
  ): Promise<OtpByPassViewModelModel> {
    // -- not used
    const response = await this.http.post<OtpByPassViewModelModel>(
      `authenticate/AuthenticateParentViaOTPByPass`,
      model,
      { baseURL: 'https://apilooppe.sn.ac.ae/gateway/Parent' }
    );
    return response;
  }

  public async getSpousesExistingDetails(
    phoneNo?: string,
    emiratesNo?: string,
  ): Promise<AuthenticateViewModelModel[]> {
    // -- not used
    const response = await this.http.get<AuthenticateViewModelModel[]>(
      `api/authenticate/GetSpousesExsitingDetails`,
      { phoneNo: phoneNo, emiratesNo: emiratesNo },
      true,
    );
    return response;
  }

  public async signUpVerifyAllByPass(
    model: ParentSignupViewModelModel,
  ): Promise<ParentSignupViewModelModel> {
    // -- not used
    const response = await this.http.post<ParentSignupViewModelModel>(
      `authenticate/SignUpVerifyAllByPass`,
      model,
      { baseURL: 'https://apilooppe.sn.ac.ae/gateway/Parent' }
    );
    return response;
  }

  public async signUpVerifyAll(
    model: ParentSignupViewModelModel,
  ): Promise<ParentSignupViewModelModel> {
    // -- not used
    const response = await this.http.post<ParentSignupViewModelModel>(
      `authenticate/SignUpVerifyAll`,
      model,
      { baseURL: 'https://apilooppe.sn.ac.ae/gateway/Parent' }
    );
    return response;
  }

  public async sendParentSignUpMobileOTPByPass(
    phoneNumber?: string,
  ): Promise<OtpByPassViewModelModel> {
    // -- not used
    const response = await this.http.get<OtpByPassViewModelModel>(
      `authenticate/SendParentSignUpMobileOTPByPass`,
      { PhoneNumber: phoneNumber },
      false,
      { baseURL: 'https://apilooppe.sn.ac.ae/gateway/Parent' }
    );
    return response;
  }

  public async sendParentSignUpMobileOTP(
    phoneNumber?: string,
  ): Promise<OtpByPassViewModelModel> {
    // -- not used
    const response = await this.http.get<OtpByPassViewModelModel>(
      `api/authenticate/SendParentSignUpMobileOTP`,
      { PhoneNumber: phoneNumber },
      false,
    );
    return response;
  }

  public async sendParentSignUpEmailOTP(
    emailId?: string,
  ): Promise<OtpByPassViewModelModel> {
    // -- not used
    const response = await this.http.get<OtpByPassViewModelModel>(
      `authenticate/SendParentSignUpEmailOTP`,
      { emailId: emailId },
      false,
      { baseURL: 'https://apilooppe.sn.ac.ae/gateway/Parent' }
    );
    return response;
  }

  public async sendParentSignUpEmailOTPByPass(
    emailId?: string,
  ): Promise<OtpByPassViewModelModel> {
    // -- not used
    const response = await this.http.get<OtpByPassViewModelModel>(
      `authenticate/SendParentSignUpEmailOTPByPass`,
      { emailId: emailId },
      false,
      { baseURL: 'https://apilooppe.sn.ac.ae/gateway/Parent' }
    );
    return response;
  }

  public async sendExistingParentEmailOTP(
    email?: string,
    parentId?: number,
  ): Promise<OtpByPassViewModelModel> {
    // -- not used
    const response = await this.http.get<OtpByPassViewModelModel>(
      `api/authenticate/SendExistingParentEmailOTP`,
      { email: email, parentId: parentId },
      false,
    );
    return response;
  }

  public async verifyExistingParentEmailOTP(
    email?: string,
    otp?: string,
    parentId?: number,
  ): Promise<OtpByPassViewModelModel> {
    // -- not used
    const response = await this.http.get<OtpByPassViewModelModel>(
      `api/authenticate/VerifyExistingParentEmailOTP`,
      { email: email, otp: otp, parentId: parentId },
      false,
    );
    return response;
  }

  public async sendExistingParentMobileOTP(
    phoneNumber?: string,
    parentId?: number,
  ): Promise<OtpByPassViewModelModel> {
    // -- not used
    const response = await this.http.get<OtpByPassViewModelModel>(
      `api/authenticate/SendExistingParentMobileOTP`,
      { phoneNumber: phoneNumber, parentId: parentId },
      false,
    );
    return response;
  }

  public async verifyExistingParentMobileOTP(
    phoneNumber?: string,
    requestId?: string,
    otp?: string,
    parentId?: number,
  ): Promise<OtpByPassViewModelModel> {
    // -- not used
    const response = await this.http.get<OtpByPassViewModelModel>(
      `api/authenticate/VerifyExistingParentMobileOTP`,
      {
        phoneNumber: phoneNumber,
        requestId: requestId,
        otp: otp,
        parentId: parentId,
      },
      false,
    );
    return response;
  }

  public async sendExistingParentMobileOTPByPass(
    phoneNumber?: string,
    parentId?: number,
  ): Promise<OtpByPassViewModelModel> {
    // -- not used
    const response = await this.http.get<OtpByPassViewModelModel>(
      `api/authenticate/SendExistingParentMobileOTPByPass`,
      { phoneNumber: phoneNumber, parentId: parentId },
      false,
    );
    return response;
  }

  public async verifyExistingParentMobileOTPByPass(
    phoneNumber?: string,
    requestId?: string,
    otp?: string,
    parentId?: number,
  ): Promise<OtpByPassViewModelModel> {
    // -- not used
    const response = await this.http.get<OtpByPassViewModelModel>(
      `api/authenticate/VerifyExistingParentMobileOTPByPass`,
      {
        phoneNumber: phoneNumber,
        requestId: requestId,
        otp: otp,
        parentId: parentId,
      },
      false,
    );
    return response;
  }

  public async createSpouseMobileOTPByPass(
    phoneNumber?: string,
  ): Promise<OtpByPassViewModelModel> {
    // -- not used
    const response = await this.http.get<OtpByPassViewModelModel>(
      `api/authenticate/CreateSpouseMobileOTPByPass`,
      { PhoneNumber: phoneNumber },
      false,
    );
    return response;
  }

  public async verifyCreateSpouseMobileOTPByPass(
    model: VerifySpouseMobileDataModel,
  ): Promise<VerifySpouseMobileDataModel> {
    // -- not used
    const response = await this.http.post<VerifySpouseMobileDataModel>(
      `api/authenticate/VerifyCreateSpouseMobileOTPByPass`,
      model,
    );
    return response;
  }

  public async signupVerifyMobileOtp(
    phoneNumber?: string,
    requestId?: string,
    otp?: string,
  ): Promise<OtpByPassViewModelModel> {
    // -- not used
    const response = await this.http.get<OtpByPassViewModelModel>(
      `authenticate/SignupVerifyMobileOtp`,
      { phoneNumber: phoneNumber, requestId: requestId, otp: otp },
      false,
      { baseURL: 'https://apilooppe.sn.ac.ae/gateway/Parent' }
    );
    return response;
  }

  public async signupVerifyEmailOtp(
    email?: string,
    otp?: string,
  ): Promise<OtpByPassViewModelModel> {
    // -- not used
    const response = await this.http.get<OtpByPassViewModelModel>(
      `authenticate/SignupVerifyEmailOtp`,
      { email: email, otp: otp },
      false,
    );
    return response;
  }

  public async signupVerifyMobileOtpByPass(
    phoneNumber?: string,
    requestId?: string,
    otp?: string,
  ): Promise<OtpByPassViewModelModel> {
    // -- not used
    const response = await this.http.get<OtpByPassViewModelModel>(
      `authenticate/SignupVerifyMobileOtpByPass`,
      { phoneNumber: phoneNumber, requestId: requestId, otp: otp },
      false,
    );
    return response;
  }

  public async deleteSpouseMobileOTP(
    spouseId?: number,
  ): Promise<DeleteSpouseViewModelModel> {
    // -- not used
    const response = await this.http.get<DeleteSpouseViewModelModel>(
      `api/authenticate/DeleteSpouseMobileOTP`,
      { spouseId: spouseId },
      false,
    );
    return response;
  }

  public async verifyDeleteSpouseMobileOTP(
    model: DeleteSpouseViewModelModel,
  ): Promise<DeleteSpouseViewModelModel> {
    // -- not used
    const response = await this.http.post<DeleteSpouseViewModelModel>(
      `api/authenticate/VerifyDeleteSpouseMobileOTP`,
      model,
    );
    return response;
  }

  public async deleteSpouseMobileOTPByPass(
    spouseId?: number,
  ): Promise<DeleteSpouseViewModelModel> {
    // -- not used
    const response = await this.http.get<DeleteSpouseViewModelModel>(
      `api/authenticate/DeleteSpouseMobileOTPByPass`,
      { spouseId: spouseId },
      false,
    );
    return response;
  }

  public async verifyDeleteSpouseMobileOTPByPass(
    model: DeleteSpouseViewModelModel,
  ): Promise<DeleteSpouseViewModelModel> {
    // -- not used
    const response = await this.http.post<DeleteSpouseViewModelModel>(
      `api/authenticate/VerifyDeleteSpouseMobileOTPByPass`,
      model,
    );
    return response;
  }
}
