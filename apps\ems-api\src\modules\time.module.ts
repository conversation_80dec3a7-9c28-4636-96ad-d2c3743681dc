import { Module } from '@nestjs/common';
import { HttpModule } from 'packages/http';
import { TimeController } from '../controllers/time.controller';
import { TimeService } from '../providers/services/time.service';

@Module({
  // -- not used
  imports: [
    HttpModule.register({
      baseURL: process.env.EMS_API_URL,
      apiTenant: 'ems',
      authTokenParams: {
        oracleCustomToken: true,
      },
    }),
  ],
  controllers: [TimeController],
  providers: [TimeService],
})
export class TimeModule {}
