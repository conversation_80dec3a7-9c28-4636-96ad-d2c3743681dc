import { Module } from '@nestjs/common';
import { HttpModule } from 'packages/http';
import { AbsenceController } from '../controllers/absence.controller';
import { HealthController } from '../controllers/health.controller';
import { AbsenceService } from '../providers/services/absence.service';

@Module({
  // -- not used
  imports: [
    HttpModule.register({
      baseURL: process.env.EMS_API_URL,
      apiTenant: 'ems',
      authTokenParams: {
        oracleCustomToken: true,
      },
      postHeaders: {
        'Content-Type': 'application/json',
      },
    }),
  ],
  controllers: [AbsenceController, HealthController],
  providers: [AbsenceService],
})
export class AbsenceModule {}
