import { Module } from '@nestjs/common';
import { HttpModule } from 'packages/http';
import { MajorsController } from '../controllers/majors.controller';
import { MajorsService } from '../providers/services/majors.service';

@Module({
  // -- not used
  imports: [
    HttpModule.register({
      baseURL: process.env.EMS_API_URL,
      apiTenant: 'ems',
      authTokenParams: {
        oracleCustomToken: true,
      },
    }),
  ],
  controllers: [MajorsController],
  providers: [MajorsService],
})
export class MajorsModule {}
