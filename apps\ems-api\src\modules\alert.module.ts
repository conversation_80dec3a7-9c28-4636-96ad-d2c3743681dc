import { Module } from '@nestjs/common';
import { HttpModule } from 'packages/http';
import { AlertController } from '../controllers/alert.controller';
import { AlertService } from '../providers/services/alert.service';

@Module({
  // -- not used
  imports: [
    HttpModule.register({
      baseURL: process.env.EMS_API_URL,
      apiTenant: 'ems',
      authTokenParams: {
        oracleCustomToken: true,
      },
    }),
  ],
  controllers: [AlertController],
  providers: [AlertService],
})
export class AlertModule {}
