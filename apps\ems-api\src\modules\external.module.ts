import { Module } from '@nestjs/common';
import { HttpModule } from 'packages/http';
import { ExternalController } from '../controllers/external.controller';
import { ExternalService } from '../providers/services/external.service';

@Module({
  // -- not used
  imports: [
    HttpModule.register({
      baseURL: process.env.EMS_API_URL,
      apiTenant: 'ems',
      authTokenParams: {
        oracleCustomToken: true,
      },
    }),
  ],
  controllers: [ExternalController],
  providers: [ExternalService],
})
export class ExternalModule {}
