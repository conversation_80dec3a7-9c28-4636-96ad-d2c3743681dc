import { Module } from '@nestjs/common';
import { HttpModule } from 'packages/http';
import { ScheduleController } from '../controllers/schedule.controller';
import { ScheduleService } from '../providers/services/schedule.service';

@Module({
  // -- not used
  imports: [
    HttpModule.register({
      baseURL: process.env.EMS_API_URL,
      apiTenant: 'ems',
      authTokenParams: {
        oracleCustomToken: true,
      },
    }),
  ],
  controllers: [ScheduleController],
  providers: [ScheduleService],
})
export class ScheduleModule {}
