import { Module } from '@nestjs/common';
import { HttpModule } from 'packages/http';
import { ReasonsController } from '../controllers/reasons.controller';
import { ReasonsService } from '../providers/services/reasons.service';

@Module({
  // -- not used
  imports: [
    HttpModule.register({
      baseURL: process.env.EMS_API_URL,
      apiTenant: 'ems',
      authTokenParams: {
        oracleCustomToken: true,
      },
    }),
  ],
  controllers: [ReasonsController],
  providers: [ReasonsService],
})
export class ReasonsModule {}
