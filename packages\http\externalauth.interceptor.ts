import {
  CallH<PERSON>ler,
  ExecutionContext,
  Injectable,
  NestInterceptor,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { RequestContext } from './request.context';

@Injectable()
export class ExternalAuthInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();

    const authorization = request.headers['authorization'];

    // Store authorization in AsyncLocalStorage
    return RequestContext.run({ authorization, request }, () => next.handle());
  }
}
