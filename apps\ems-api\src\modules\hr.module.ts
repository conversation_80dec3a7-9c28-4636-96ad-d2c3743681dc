import { Module } from '@nestjs/common';
import { HttpModule } from 'packages/http';
import { HrController } from '../controllers/hr.controller';
import { HrService } from '../providers/services/hr.service';

@Module({
  // -- not used
  imports: [
    HttpModule.register({
      baseURL: process.env.EMS_API_URL,
      apiTenant: 'ems',
      authTokenParams: {
        oracleCustomToken: true,
      },
    }),
  ],
  controllers: [HrController],
  providers: [HrService],
})
export class HrModule {}
