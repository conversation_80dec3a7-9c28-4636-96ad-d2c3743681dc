import { Module } from '@nestjs/common';
import { HttpModule } from 'packages/http';
import { EmpsController } from '../controllers/emps.controller';
import { EmpsService } from '../providers/services/emps.service';

@Module({
  // -- not used
  imports: [
    HttpModule.register({
      baseURL: process.env.EMS_API_URL,
      apiTenant: 'ems',
      authTokenParams: {
        oracleCustomToken: true,
      },
    }),
  ],
  controllers: [EmpsController],
  providers: [EmpsService],
})
export class EmpsModule {}
