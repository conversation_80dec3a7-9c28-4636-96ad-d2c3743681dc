import { Module } from '@nestjs/common';
import { HttpModule } from 'packages/http';
import { MyController } from '../controllers/my.controller';
import { MyService } from '../providers/services/my.service';

@Module({
  // -- not used
  imports: [
    HttpModule.register({
      baseURL: process.env.EMS_API_URL,
      apiTenant: 'ems',
      authTokenParams: {
        oracleCustomToken: true,
      },
    }),
  ],
  controllers: [MyController],
  providers: [MyService],
})
export class MyModule {}
