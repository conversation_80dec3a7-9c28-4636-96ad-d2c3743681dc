import { Module } from '@nestjs/common';
import { HttpModule } from 'packages/http';
import { ActionController } from '../controllers/action.controller';
import { ActionService } from '../providers/services/action.service';

@Module({
  // -- not used
  imports: [
    HttpModule.register({
      baseURL: process.env.EMS_API_URL,
      apiTenant: 'ems',
      authTokenParams: {
        oracleCustomToken: true,
      },
    }),
  ],
  controllers: [ActionController],
  providers: [ActionService],
})
export class ActionModule {}
