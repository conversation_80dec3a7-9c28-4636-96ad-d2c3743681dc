import { MaritalStatusModel } from '../family/maritalStatus.model';
import { HealthStatusModel } from '../health/healthStatus.model';
import { EmirateModel } from '../location/emirate.model';
import { ParentModel } from '../parent/parent.model';
// ParentProfile
/** */
export class ParentProfileModel {
  // -- not used
  public id: number;
  public Id: number;
  public fullNameInEnglish?: string;
  public fullNameInArabic?: string;
  public gender?: string;
  public nationality?: string;
  public parentId?: number;
  public passportDocumentUpload?: string;
  public passportEmirate?: string;
  public passportNo?: string;
  public passportBirthPlace?: string;
  public issuingAuthority?: string;
  public familyNo?: string;
  public dob?: string; // date-time
  public passportExpiry?: string; // date-time
  public passportEmirateId?: number;
  public emiratesDocumentUploadFront?: string;
  public emiratesDocumentUploadBack?: string;
  public emiratesNo?: string;
  public emiratesExpiry?: string; // date-time
  public maritalStatusId?: number;
  public healthStatusId?: number;
  public isDisabled?: boolean;
  public isPeopleofDetermination?: boolean;
  public determinationCertificateUpload?: string;
  public healthDocumentUpload?: string;
  public createdBy?: number;
  public createdDate?: string;
  public lastUpdatedBy?: number;
  public lastUpdatedDate?: string;
  public isDeleted?: boolean;
  public currentWizardNo?: number;
  public profileDocumentUpload?: string;
  public maritalStatus: MaritalStatusModel;
  public healthStatus: HealthStatusModel;
  public emirate: EmirateModel;
  public parent: ParentModel;
  public PhoneNumber?: string;
  public Email?: string;
  public ParentProfiles?: ParentProfileModel[];
}
