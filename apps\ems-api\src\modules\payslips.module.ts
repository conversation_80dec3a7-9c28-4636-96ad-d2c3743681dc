import { Module } from '@nestjs/common';
import { HttpModule } from 'packages/http';
import { PayslipsController } from '../controllers/payslips.controller';
import { PayslipsService } from '../providers/services/payslips.service';

@Module({
  // -- not used
  imports: [
    HttpModule.register({
      baseURL: process.env.EMS_API_URL,
      apiTenant: 'ems',
      authTokenParams: {
        oracleCustomToken: true,
      },
    }),
  ],
  controllers: [PayslipsController],
  providers: [PayslipsService],
})
export class PayslipsModule {}
