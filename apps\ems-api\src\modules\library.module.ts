import { Module } from '@nestjs/common';
import { HttpModule } from 'packages/http';
import { LibraryController } from '../controllers/library.controller';
import { LibraryService } from '../providers/services/library.service';

@Module({
  // -- not used
  imports: [
    HttpModule.register({
      baseURL: process.env.EMS_API_URL,
      apiTenant: 'ems',
      authTokenParams: {
        oracleCustomToken: true,
      },
    }),
  ],
  controllers: [LibraryController],
  providers: [LibraryService],
})
export class LibraryModule {}
