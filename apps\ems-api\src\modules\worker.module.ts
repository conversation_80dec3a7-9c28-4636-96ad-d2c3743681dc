import { Module } from '@nestjs/common';
import { HttpModule } from 'packages/http';
import { WorkerController } from '../controllers/worker.controller';
import { WorkerService } from '../providers/services/worker.service';

@Module({
  // -- not used
  imports: [
    HttpModule.register({
      baseURL: process.env.EMS_API_URL,
      apiTenant: 'ems',
      authTokenParams: {
        oracleCustomToken: true,
      },
    }),
  ],
  controllers: [WorkerController],
  providers: [WorkerService],
})
export class WorkerModule {}
