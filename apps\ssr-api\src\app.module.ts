import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { TypeOrmModule } from '@nestjs/typeorm';
import { configService } from 'apps/auth-api/src/config/config.service';
import { Token } from 'apps/auth-api/src/entities/token.entity';
import { User } from 'apps/auth-api/src/entities/user.entity';
import { UserProfile } from 'apps/auth-api/src/entities/userProfile.entity';
import { DefaultHttpModules } from './moduleInit';
import { moduleExports } from './modules';
const typeOrmSettings = Object.assign(configService.getTypeOrmConfig(), {
  autoLoadEntities: true,
  entities: [User, Token, UserProfile],
});
@Module({
  imports: [
    TypeOrmModule.forRoot(typeOrmSettings),
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath:
        process.env.NODE_ENV === 'production'
          ? '.env.production'
          : '.env.development',
    }),
    DefaultHttpModules(),
    ...moduleExports,
    PassportModule.register({ defaultStrategy: 'jwt' }),
    JwtModule.register({
      secret: process.env.TOKEN_SECRET || 'defaultSecretKey',
      signOptions: { expiresIn: '1y' },
    }),
  ],
  controllers: [],
  providers: [
    // {
    //   provide: APP_GUARD,
    //   useClass: JwtAuthGuard,
    // },
    // JwtService,
    // JwtStrategy,
  ],
})
export class AppModule {}
