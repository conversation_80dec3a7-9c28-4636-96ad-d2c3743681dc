import { Module } from '@nestjs/common';
import { HttpModule } from 'packages/http';
import { BargainingController } from '../controllers/bargaining.controller';
import { BargainingService } from '../providers/services/bargaining.service';

@Module({
  // -- not used
  imports: [
    HttpModule.register({
      baseURL: process.env.EMS_API_URL,
      apiTenant: 'ems',
      authTokenParams: {
        oracleCustomToken: true,
      },
    }),
  ],
  controllers: [BargainingController],
  providers: [BargainingService],
})
export class BargainingModule {}
