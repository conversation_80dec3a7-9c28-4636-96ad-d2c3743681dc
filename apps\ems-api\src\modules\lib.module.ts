import { Module } from '@nestjs/common';
import { HttpModule } from 'packages/http';
import { LibController } from '../controllers/lib.controller';
import { LibService } from '../providers/services/lib.service';

@Module({
  // -- not used
  imports: [
    HttpModule.register({
      baseURL: process.env.EMS_API_URL,
      apiTenant: 'ems',
      authTokenParams: {
        oracleCustomToken: true,
      },
    }),
  ],
  controllers: [LibController],
  providers: [LibService],
})
export class LibModule {}
