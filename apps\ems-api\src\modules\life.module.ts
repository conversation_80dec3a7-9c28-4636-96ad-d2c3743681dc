import { Module } from '@nestjs/common';
import { HttpModule } from 'packages/http';
import { LifeController } from '../controllers/life.controller';
import { LifeService } from '../providers/services/life.service';

@Module({
  // -- not used
  imports: [
    HttpModule.register({
      baseURL: process.env.EMS_API_URL,
      apiTenant: 'ems',
      authTokenParams: {
        oracleCustomToken: true,
      },
    }),
  ],
  controllers: [LifeController],
  providers: [LifeService],
})
export class LifeModule {}
