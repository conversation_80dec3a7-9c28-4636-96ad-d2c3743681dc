import { HttpModule } from 'packages/http';
const url = process.env.HALO_ITMS_AUTH_URL;
const clientId = process.env.HALO_ITMS_CLIENT_ID;
const clientSecret = process.env.HALO_ITMS_CLIENT_SECRET;

export const DefaultHttpModules = () => {
  return HttpModule.register({
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    postHeaders: {
      'Content-Type': 'application/json',
    },
    baseURL: process.env.SSR_API_URL,
    authTokenUrl: url + '/auth/token',
    authTokenResponseKey: 'access_token',
    apiTenant: 'ssr',
    authTokenParams: {
      grant_type: 'client_credentials',
      scope: 'all',
      client_id: clientId,
      client_secret: clientSecret,
    },
  });
};
