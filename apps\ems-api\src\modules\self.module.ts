import { Module } from '@nestjs/common';
import { HttpModule } from 'packages/http';
import { SelfController } from '../controllers/self.controller';
import { SelfService } from '../providers/services/self.service';

@Module({
  // -- not used
  imports: [
    HttpModule.register({
      baseURL: process.env.EMS_API_URL,
      apiTenant: 'ems',
      authTokenParams: {
        oracleCustomToken: true,
      },
    }),
  ],
  controllers: [SelfController],
  providers: [SelfService],
})
export class SelfModule {}
