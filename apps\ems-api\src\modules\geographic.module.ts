import { Module } from '@nestjs/common';
import { HttpModule } from 'packages/http';
import { GeographicController } from '../controllers/geographic.controller';
import { GeographicService } from '../providers/services/geographic.service';

@Module({
  // -- not used
  imports: [
    HttpModule.register({
      baseURL: process.env.EMS_API_URL,
      apiTenant: 'ems',
      authTokenParams: {
        oracleCustomToken: true,
      },
    }),
  ],
  controllers: [GeographicController],
  providers: [GeographicService],
})
export class GeographicModule {}
