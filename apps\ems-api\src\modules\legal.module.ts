import { Module } from '@nestjs/common';
import { HttpModule } from 'packages/http';
import { LegalController } from '../controllers/legal.controller';
import { LegalService } from '../providers/services/legal.service';

@Module({
  // -- not used
  imports: [
    HttpModule.register({
      baseURL: process.env.EMS_API_URL,
      apiTenant: 'ems',
      authTokenParams: {
        oracleCustomToken: true,
      },
    }),
  ],
  controllers: [LegalController],
  providers: [LegalService],
})
export class LegalModule {}
