import { Module } from '@nestjs/common';
import { HttpModule } from 'packages/http';
import { ActionsController } from '../controllers/actions.controller';
import { ActionsService } from '../providers/services/actions.service';

@Module({
  // -- not used
  imports: [
    HttpModule.register({
      baseURL: process.env.EMS_API_URL,
      apiTenant: 'ems',
      authTokenParams: {
        oracleCustomToken: true,
      },
    }),
  ],
  controllers: [ActionsController],
  providers: [ActionsService],
})
export class ActionsModule {}
