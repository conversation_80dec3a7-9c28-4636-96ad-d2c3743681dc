import { Module } from '@nestjs/common';
import { HttpModule } from 'packages/http';
import { ElementController } from '../controllers/element.controller';
import { ElementService } from '../providers/services/element.service';

@Module({
  // -- not used
  imports: [
    HttpModule.register({
      baseURL: process.env.EMS_API_URL,
      apiTenant: 'ems',
      authTokenParams: {
        oracleCustomToken: true,
      },
    }),
  ],
  controllers: [ElementController],
  providers: [ElementService],
})
export class ElementModule {}
