import { Module } from '@nestjs/common';
import { HttpModule } from 'packages/http';
import { EmailController } from '../controllers/email.controller';
import { EmailService } from '../providers/services/email.service';

@Module({
  // -- not used
  imports: [
    HttpModule.register({
      baseURL: process.env.EMS_API_URL,
      apiTenant: 'ems',
      authTokenParams: {
        oracleCustomToken: true,
      },
    }),
  ],
  controllers: [EmailController],
  providers: [EmailService],
})
export class EmailModule {}
