import { Module } from '@nestjs/common';
import { HttpModule } from 'packages/http';
import { SearchController } from '../controllers/search.controller';
import { SearchService } from '../providers/services/search.service';

@Module({
  // -- not used
  imports: [
    HttpModule.register({
      baseURL: process.env.EMS_API_URL,
      apiTenant: 'ems',
      authTokenParams: {
        oracleCustomToken: true,
      },
    }),
  ],
  controllers: [SearchController],
  providers: [SearchService],
})
export class SearchModule {}
