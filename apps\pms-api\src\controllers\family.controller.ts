import {
  Body,
  Controller,
  Get,
  Post,
  Query,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { FamilyModel } from '../providers/models/family/family.model';
import { MaritalStatusModel } from '../providers/models/family/maritalStatus.model';
import { FamilyService } from '../providers/services/family.service';
@Controller()
export class FamilyController {
  constructor(private readonly service: FamilyService) {}

  @Post('family/addUpdateFamily')
  public async addUpdateFamily(
    @Body() body: FamilyModel,
  ): Promise<FamilyModel> {
    // -- not used
    const response = await this.service.addUpdateFamily(body);
    return response;
  }

  @Post('family/addUpdateSpouse')
  public async addUpdateSpouse(
    @Body() body: MaritalStatusModel,
  ): Promise<MaritalStatusModel> {
    // -- not used
    const response = await this.service.addUpdateSpouse(body);
    return response;
  }

  @Post('upload/UploadFileToS3')
  @UseInterceptors(FileInterceptor('file'))
  async uploadFileToS3(@UploadedFile() file: Express.Multer.File) {
    return this.service.uploadFileToS3(file);
  }

  @Get('family/getFamilyDetailsByParentId')
  public async getFamilyDetailsByParentId(
    fields?: string,
    pageNumber?: number,
    pageSize?: number,
    id?: number,
    orderBy?: string,
    parentId?: number,
  ): Promise<FamilyModel[]> {
    // -- not used
    const response = await this.service.getFamilyDetailsByParentId(
      fields,
      pageNumber,
      pageSize,
      id,
      orderBy,
      parentId,
    );
    return response;
  }

  @Get('family/removeFamilySpouseKid')
  public async removeFamilySpouseKid(
    familyId?: number,
    spouseId?: number,
    kidId?: number,
  ): Promise<FamilyModel> {
    // -- not used
    const response = await this.service.removeFamilySpouseKid(
      familyId,
      spouseId,
      kidId,
    );
    return response;
  }

  @Get('family/getFamilyDetailsByParentSpouseId')
  public async getFamilyDetailsByParentSpouseId(
    @Query('parentId') parentId: string,
  ): Promise<FamilyModel[]> {
    // -- not used
    const response =
      await this.service.getFamilyDetailsByParentSpouseId(parentId);
    return response;
  }

  @Get('family/getFamilyDetailsByFamilyId')
  public async getFamilyDetailsByFamilyId(
    fields?: string,
    pageNumber?: number,
    pageSize?: number,
    id?: number,
    orderBy?: string,
    familyId?: number,
    parentId?: number,
  ): Promise<FamilyModel[]> {
    // -- not used
    const response = await this.service.getFamilyDetailsByFamilyId(
      fields,
      pageNumber,
      pageSize,
      id,
      orderBy,
      familyId,
      parentId,
    );
    return response;
  }

  @Get('family/getFamilySummaryByFamilyId')
  public async getFamilySummaryByFamilyId(
    familyId?: number,
  ): Promise<FamilyModel[]> {
    // -- not used
    const response = await this.service.getFamilySummaryByFamilyId(familyId);
    return response;
  }
}
