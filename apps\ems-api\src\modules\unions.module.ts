import { Module } from '@nestjs/common';
import { HttpModule } from 'packages/http';
import { UnionsController } from '../controllers/unions.controller';
import { UnionsService } from '../providers/services/unions.service';

@Module({
  // -- not used
  imports: [
    HttpModule.register({
      baseURL: process.env.EMS_API_URL,
      apiTenant: 'ems',
      authTokenParams: {
        oracleCustomToken: true,
      },
    }),
  ],
  controllers: [UnionsController],
  providers: [UnionsService],
})
export class UnionsModule {}
