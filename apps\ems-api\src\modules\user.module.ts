import { Module } from '@nestjs/common';
import { HttpModule } from 'packages/http';
import { UserController } from '../controllers/user.controller';
import { UserService } from '../providers/services/user.service';

@Module({
  // -- not used
  imports: [
    HttpModule.register({
      baseURL: process.env.EMS_API_URL,
      apiTenant: 'ems',
      authTokenParams: {
        oracleCustomToken: true,
      },
    }),
  ],
  controllers: [UserController],
  providers: [UserService],
})
export class UserModule {}
