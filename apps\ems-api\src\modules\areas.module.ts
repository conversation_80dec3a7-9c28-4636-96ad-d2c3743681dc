import { Module } from '@nestjs/common';
import { HttpModule } from 'packages/http';
import { AreasController } from '../controllers/areas.controller';
import { AreasService } from '../providers/services/areas.service';

@Module({
  // -- not used
  imports: [
    HttpModule.register({
      baseURL: process.env.EMS_API_URL,
      apiTenant: 'ems',
      authTokenParams: {
        oracleCustomToken: true,
      },
    }),
  ],
  controllers: [AreasController],
  providers: [AreasService],
})
export class AreasModule {}
