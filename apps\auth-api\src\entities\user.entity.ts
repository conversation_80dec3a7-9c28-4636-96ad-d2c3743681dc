import {
  Column,
  CreateDateColumn,
  <PERSON>tity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity('users')
export class User {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'name', nullable: true, length: 300 })
  name: string;

  @Column({ name: 'email', nullable: true, length: 1000 })
  email?: string;

  @Column({ name: 'emails', nullable: true, length: 4000 })
  emails?: string;

  @Column({ name: 'phone', nullable: true, length: 30 })
  phone?: string;

  @Column({ name: 'phones', nullable: true, length: 2000 })
  phones?: string;

  @Column({
    name: 'office365_id',
    nullable: true,

    length: 1000,
  })
  office365Id: string;

  @Column({
    name: 'date_of_birth',
    nullable: true,

    length: 30,
  })
  DateOfBirth?: string;

  @Column({ name: 'gender', nullable: true, length: 200 })
  Gender?: string;

  @Column({ name: 'password', nullable: true, length: 200 })
  password: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column('bit', {
    name: 'is_active',
    nullable: true,
    default: false,
    transformer: {
      from: (value: Buffer) => (value != null ? Boolean(value[0]) : false),
      to: (value: boolean) => (value ? 1 : 0),
    },
  })
  isActive: boolean;
}
