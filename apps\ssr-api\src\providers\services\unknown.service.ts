import { Injectable } from '@nestjs/common';
import { HttpService } from 'packages/http';
import { StringStringValuesKeyValuePairModel } from '../models/unknown/stringStringValuesKeyValuePair.model';
@Injectable()
export class UnknownService {
  constructor(private readonly http: HttpService) {}

  public async getSnelStart(
    datatype?: string,
  ): Promise<StringStringValuesKeyValuePairModel> {
    // -- not used
    const response = await this.http.get<StringStringValuesKeyValuePairModel>(
      `IntegrationData/Get/SnelStart`,
      { datatype: datatype },
      false,
      { baseURL: 'https://apilooppe.sn.ac.ae/gateway/Parent' }
    );
    return response;
  }

  public async getSearch(
    count_per_entity?: number,
    search?: string,
  ): Promise<any> {
    // -- not used
    const response = await this.http.get(
      `Search`,
      { count_per_entity: count_per_entity, search: search },
      false,
    );
    return response;
  }

  public async getZendesk(): Promise<any> {
    // -- not used
    const response = await this.http.get(`Zendesk/Get`, {}, false);
    return response;
  }
}
