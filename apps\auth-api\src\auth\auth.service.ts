import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import * as crypto from 'crypto';
import * as jwt from 'jsonwebtoken';
import { jwtDecode } from 'jwt-decode';
import { HttpService } from 'packages/http';
import { Repository } from 'typeorm';
import { Token } from '../entities/token.entity';
import { User } from '../entities/user.entity';
import { UserProfile } from '../entities/userProfile.entity';
import { JwtPayload } from './jwt-payload.interface';

@Injectable()
export class AuthService {
  tokenGenerate(payload: JwtPayload): {
    accessToken: string;
    refreshToken: string;
  } {
    const accessToken = jwt.sign(payload, this.tokenSecret ?? '12345', {
      expiresIn: '1h',
    });

    const refreshToken = jwt.sign(
      { userId: payload.userId },
      this.tokenSecret ?? '12345',
      { expiresIn: '7d' },
    );

    return { accessToken, refreshToken };
  }
  private readonly clientId?: string;
  private readonly clientSecret?: string;
  private readonly tokenSecret?: string;
  private readonly tenantId?: string;
  private readonly redirectUri?: string;
  private readonly etisalatApiUrl?: string;
  private readonly etisalatApiPort?: string;

  constructor(
    private configService: ConfigService,
    private httpService: HttpService,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(UserProfile)
    private userProfileRepository: Repository<UserProfile>,
    @InjectRepository(Token)
    private tokenRepository: Repository<Token>,
  ) {
    this.clientId = this.configService.get<string>('OFFICE_CLIENT_ID');
    this.clientSecret = this.configService.get<string>('OFFICE_CLIENT_SECRET');
    this.tokenSecret = this.configService.get<string>('TOKEN_SECRET');
    this.tenantId = this.configService.get<string>('OFFICE_TENANT_ID');
    this.redirectUri = this.configService.get<string>('REDIRECT_URI');
  }

  // Generates a code verifier and the corresponding code challenge
  public generateCodeChallengeAndVerifier(): {
    codeVerifier: string;
    codeChallenge: string;
  } {
    const codeVerifier = this.generateRandomString(128); // Random string for code verifier
    const codeChallenge = this.generateCodeChallenge(codeVerifier); // Generate code challenge
    return { codeVerifier, codeChallenge };
  }

  generateRandomString(length: number): string {
    return crypto.randomBytes(length).toString('base64url'); // URL-safe base64 encoding
  }
  generateCodeChallenge(codeVerifier: string): string {
    const sha256 = crypto
      .createHash('sha256')
      .update(codeVerifier)
      .digest('base64url');
    return sha256;
  }
  async exchangeAuthorizationCodeForToken(
    code: string,
    codeVerifier: string,
    redirectUri: string,
  ): Promise<any> {
    const tokenEndpoint = `${this.tenantId}/oauth2/v2.0/token`;
    //let codeDigest = this.generateCodeChallenge(codeVerifier);
    const formData = new FormData();
    // formData.append('client_id', this.clientId ?? '');
    // formData.append('scope', encodeURIComponent('openid profile email'));
    formData.append('code', code);
    formData.append('redirect_uri', redirectUri);
    formData.append('grant_type', 'authorization_code');
    // formData.append(
    //   'client_secret',
    //   encodeURIComponent(this.clientSecret ?? ''),
    // );
    // formData.append('code_verifier', codeVerifier);

    const postData = {
      code: code,
      redirect_uri: redirectUri,
      grant_type: 'authorization_code',
    };

    try {
      const response = await this.httpService.post(tokenEndpoint, postData, {
        baseURL: 'https://login.microsoftonline.com',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          Authorization: `Basic ${Buffer.from(
            `${this.clientId}:${this.clientSecret}`,
          ).toString('base64')}`,
        },
      });
      console.log('response', response);
      return response;
    } catch (e) {
      console.log('error', e);
    }

    return null;
  }

  async getTokenData(codeVerifier: string): Promise<any> {
    const res = await this.tokenRepository.findOne({
      where: { clientCode: codeVerifier },
    });
    if (res?.userId) {
      const user = await this.userRepository.findOne({
        where: { id: res.userId },
      });

      const userProfiles = await this.userProfileRepository.find({
        where: { userId: res.userId },
      });
      if (user) {
        await this.tokenRepository.delete(res.id);
        return [
          this.generateToken(user, userProfiles, res),
          user,
          userProfiles,
        ];
      }
    }
    return null;
  }

  async storeTokenAndGenerateNewToken(data: {
    access_token: string;
    codeVerifier: string;
    refresh_token: string;
    id_token: string;
    name: string;
    office365Id: string;
  }): Promise<string> {
    const { access_token, refresh_token, id_token, codeVerifier } = data;

    const readToken: any = jwtDecode(id_token);
    //  console.log('readToken', readToken);
    const { email, exp } = readToken;
    //  const readAccessToken = jwtDecode(access_token);
    //  console.log('readAccessToken', readAccessToken);

    // Find or create the user
    const user = await this.userRepository.findOne({ where: { email } });
    if (!user) {
      throw new Error('User not found');
    }
    // if (!user) {
    //   user = this.userRepository.create({ email, name, office365Id });
    //   await this.userRepository.save(user);
    // }

    // Store the tokens in the database
    const token = this.tokenRepository.create({
      user,
      accessToken: access_token,
      refreshToken: refresh_token ?? 'not available',
      clientCode: codeVerifier,
      tokenType: 'Office365',
      expiresIn: exp,
      idToken: id_token,
    });

    const tkResopnse = await this.tokenRepository.save(token);

    const userProfiles = await this.userProfileRepository.find({
      where: { user: user },
    });
    if (!userProfiles) {
      throw new Error('User Profile not found');
    }

    // Generate a new token
    const newToken = this.generateToken(user, userProfiles, tkResopnse);
    return newToken;
  }

  async refreshAccessToken(oldRefreshToken: string): Promise<any> {
    try {
      const tokenEndpoint = `https://login.microsoftonline.com/${this.tenantId}/oauth2/v2.0/token`;
      console.log('Token endpoint:', tokenEndpoint);

      // Prepare form data for token refresh
      const formData = new URLSearchParams();
      formData.append('client_id', this.clientId ?? '');
      formData.append('grant_type', 'refresh_token');
      formData.append('refresh_token', oldRefreshToken);
      formData.append('client_secret', this.clientSecret ?? '');
      formData.append(
        'scope',
        'openid profile email User.Read Calendars.Read Calendars.ReadWrite Calendars.ReadBasic',
      );
      // Use native fetch instead of HttpService to avoid baseURL issues
      const response = await fetch(tokenEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: formData.toString(),
      });

      console.log('Refresh token response received:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.log('HTTP error response:', response.status, errorText);

        if (response.status === 400 || response.status === 401) {
          throw new Error(
            'Refresh token expired - user needs to re-authenticate',
          );
        } else {
          throw new Error(`HTTP ${response.status}: ${errorText}`);
        }
      }

      const responseData = await response.json();
      console.log('Response data keys:', Object.keys(responseData || {}));

      if (responseData && responseData.access_token) {
        console.log('New access token received successfully');
        return responseData;
      } else {
        console.log('No access token in response:', responseData);
        throw new Error('No access token received from Microsoft');
      }
    } catch (error) {
      console.error('Error refreshing token:', {
        message: error.message,
        name: error.name,
        stack: error.stack?.split('\n')[0], // Just first line of stack
      });

      // Provide more specific error message
      if (error.message.includes('Refresh token expired')) {
        throw new Error(
          'Refresh token expired - user needs to re-authenticate',
        );
      } else {
        throw new Error(`Token refresh failed: ${error.message}`);
      }
    }
  }

  private generateToken(
    user: User,
    userProfiles: UserProfile[],
    token: Token,
  ): string {
    const halloProfile =
      userProfiles.find((profile) => profile.Type?.startsWith('SSR')) ?? {};
    const fusionProfile =
      userProfiles.find((profile) => profile.Type?.startsWith('EMS')) ?? {};
    const nmsProfile = userProfiles.find((profile) =>
      profile.Type?.startsWith('NMS'),
    );
    const pmsProfile = userProfiles.find((profile) =>
      profile.Type?.startsWith('PMS'),
    );

    const payload: JwtPayload = {
      userId: user.id,
      name: user.name,
      email: user.email ?? '',
      role: 'Employee',
      adToken: token.accessToken,
      adExpiresIn: token.expiresIn,
      adRefreshToken: token.refreshToken,
      halloToken: '',
      halloExpiresIn: 0,
      fusionToken: '',
      fusionExpiresIn: 0,
      nmsToken: '',
      nmsExpiresIn: 0,
      pmsToken: '',
      pmsExpiresIn: 0,
      fusionProfile: fusionProfile ? JSON.stringify(fusionProfile) : '',
      nmsProfile: nmsProfile ? JSON.stringify(nmsProfile) : '',
      pmsProfile: pmsProfile ? JSON.stringify(pmsProfile) : '',
      halloProfile: halloProfile ? JSON.stringify(halloProfile) : '',
      adProfile: '',
    };

    const accessToken = jwt.sign(payload, this.tokenSecret ?? '12345', {
      expiresIn: '10y',
    });
    return accessToken;
  }

  async getAllProfiles(): Promise<any[]> {
    return await this.userProfileRepository.find();
  }

  async getProfileByPersonId(personId: any): Promise<any> {
    return await this.userProfileRepository.findOneBy({
      PersonId: personId,
    });
  }

  /**
   * This method is for handling Parent OTP login.
   * It finds or creates a user by phone number, saves the token with tokenType 'parentOTP',
   * and uses the PMS profile for the user. It returns a new JWT token.
   * @param data - contains access_token, codeVerifier, requestId, phoneNumber, name
   */
  async storeTokenAndGenerateNewTokenForParent(data: {
    access_token: string;
    // codeVerifier: string;
    // requestId: string;
    phoneNumber: string;
    name?: string;
    email?: string;
  }): Promise<{ token: string; user: any; userProfiles: any[] }> {
    const { access_token, phoneNumber, name, email } = data;

    // Find user by email
    let user = await this.userRepository.findOne({
      where: { email: email },
    });
    if (!user) {
      // If not found, create a new user with email
      user = this.userRepository.create({
        email: email,
        name: name || '',
        isActive: true,
      });
      user = await this.userRepository.save(user);
    }

    // Save the token in the tokenRepository
    const token = this.tokenRepository.create({
      user,
      accessToken: access_token,
      clientCode: '',
      tokenType: 'parentOTP',
      expiresIn: Math.floor(Date.now() / 1000) + 60 * 60 * 24 * 30, // 30 days expiry
      idToken: '',
      refreshToken: '',
    });
    const tkResponse = await this.tokenRepository.save(token);

    // Get the PMS profile for this user
    let userProfiles = await this.userProfileRepository.find({
      where: { user: user, Type: 'PMS' },
    });
    // If not found, create a dummy PMS profile
    if (!userProfiles || userProfiles.length === 0) {
      const newProfile = this.userProfileRepository.create({
        user: user,
        userId: user.id,
        Type: 'PMS',
        name: name || '',
        phone: phoneNumber,
        isActive: true,
      });
      await this.userProfileRepository.save(newProfile);
      userProfiles = [newProfile];
    }

    // Generate a new token (JWT)
    const newToken = this.generateToken(user, userProfiles, tkResponse);
    return { token: newToken, user, userProfiles };
  }

  /**
   * This method is for handling Parent SignUp via Phone Number.
   * It finds or creates a user by phone number, saves the token with tokenType 'parentSignUp',
   * and uses the PMS profile for the user. It returns a new JWT token.
   * @param data - contains jwtToken, phoneNumber, parentId, name (optional)
   */
  async storeTokenAndGenerateNewTokenForParentForSignUp(data: {
    jwtToken: string;
    phoneNumber: string;
    parentId: string | number;
    name?: string;
    email?: string;
  }): Promise<{ token: string; user: any; userProfiles: any[] }> {
    const { jwtToken, phoneNumber, name, email } = data;

    let user;
    if (email && email.trim() !== '') {
      // Search by email or phone
      user = await this.userRepository.findOne({
        where: [{ email: email }, { phone: phoneNumber }],
      });
    } else {
      // Search by phone only
      user = await this.userRepository.findOne({
        where: { phone: phoneNumber },
      });
    }
    if (!user) {
      // If not found, create a new user with phone and email if available
      user = this.userRepository.create({
        email: email || '',
        phone: phoneNumber,
        name: name || '',
        isActive: true,
      });
      user = await this.userRepository.save(user);
    }

    // Save the token in the tokenRepository
    const token = this.tokenRepository.create({
      user,
      accessToken: jwtToken,
      clientCode: '',
      tokenType: 'parentSignUp',
      expiresIn: Math.floor(Date.now() / 1000) + 60 * 60 * 24 * 30, // 30 days expiry
      idToken: '',
      refreshToken: '',
    });
    const tkResponse = await this.tokenRepository.save(token);

    // Get the PMS profile for this user
    let userProfiles = await this.userProfileRepository.find({
      where: { user: user, Type: 'PMS' },
    });
    // If not found, create a dummy PMS profile
    if (!userProfiles || userProfiles.length === 0) {
      const newProfile = this.userProfileRepository.create({
        user: user,
        userId: user.id,
        Type: 'PMS',
        name: name || '',
        phone: phoneNumber,
        isActive: true,
        email: email || '',
      });
      await this.userProfileRepository.save(newProfile);
      userProfiles = [newProfile];
    }

    // Generate a new token (JWT)
    const newToken = this.generateToken(user, userProfiles, tkResponse);
    return { token: newToken, user, userProfiles };
  }
  async findUserOffice365Token(userId: number): Promise<Token | null> {
    try {
      const token = await this.tokenRepository.findOne({
        where: {
          userId: userId,
          tokenType: 'Office365',
        },
        order: { id: 'DESC' }, // Get the latest token
      });

      if (token) {
        console.log(`🔍 Found Office365 token for user ${userId}`);
        if (token.refreshToken && token.refreshToken !== 'not available') {
          console.log(
            `User ${userId} has refresh token: ${token.refreshToken.substring(0, 50)}...`,
          );
        }
      } else {
        console.log(` No Office365 token found for user ${userId}`);
      }

      return token;
    } catch (error) {
      console.log(` Error finding token for user ${userId}:`, error.message);
      return null;
    }
  }

  // Helper method to update token record
  async updateTokenRecord(
    tokenId: number,
    updateData: Partial<Token>,
  ): Promise<void> {
    try {
      await this.tokenRepository.update(tokenId, updateData);
      console.log(`Updated token record ${tokenId}`);

      // Log what was updated
      if (updateData.accessToken) {
        console.log(
          ` Updated access token: ${updateData.accessToken.substring(0, 50)}...`,
        );
      }
      if (updateData.refreshToken) {
        console.log(
          ` Updated refresh token: ${updateData.refreshToken.substring(0, 50)}...`,
        );
      }
      if (updateData.expiresIn) {
        const expiryDate = new Date(updateData.expiresIn * 1000);
        console.log(
          ` Updated expiry time: ${updateData.expiresIn} (${expiryDate.toISOString()})`,
        );
      }
    } catch (error) {
      console.log(` Error updating token record ${tokenId}:`, error.message);
      throw error;
    }
  }
  //   public async loginViaEtisalat(
  //   username: string,
  //   password: string,
  // ): Promise<EtisalatLoginResponse> {
  //   try {
  //     const loginUrl = `${process.env.ETISALAT_BASE_URL}/login/user`;
  //     const payload: EtisalatLoginRequest = { username, password };

  //     const response: AxiosResponse<EtisalatLoginResponse | EtisalatErrorResponse> =
  //       await this.httpService.post(loginUrl, payload);

  //     if ('msg' in response.data) {
  //       // Handle error response
  //       throw new Error(`Etisalat authentication failed: ${response.data.msg}`);
  //     }

  //     return response.data;
  //   } catch (error) {
  //     // Handle different types of errors
  //     if (error.response) {
  //       // The request was made and the server responded with a status code
  //       throw new Error(`Etisalat API responded with status ${error.response.status}: ${error.response.data?.msg || 'Unknown error'}`);
  //     } else if (error.request) {
  //       // The request was made but no response was received
  //       throw new Error('No response received from Etisalat API');
  //     } else {
  //       // Something happened in setting up the request
  //       throw new Error(`Error setting up Etisalat API request: ${error.message}`);
  //     }
  //   }
  // }

  // public async loginViaEtisalat(
  //   username: string,
  //   password: string,
  // ): Promise<any> {
  //   try {
  //     // Get base URL from environment variables
  //     const baseUrl = this.configService.get<string>('ETISALAT_BASE_URL');
  //     if (!baseUrl) {
  //       throw new Error('ETISALAT_BASE_URL is not configured');
  //     }
  //     // Construct the full URL
  //     const loginUrl = `${baseUrl}/login/user`;
  //     // Prepare the request payload
  //     const payload = {
  //       username: username.trim(),
  //       password: password.trim(),
  //     };
  //     // Make the API call
  //     const response: AxiosResponse = await this.httpService.post(
  //       loginUrl,
  //       payload,
  //       {
  //         headers: {
  //           'Content-Type': 'application/json',
  //         },
  //       }
  //     );
  //     return response.data;
  //   } catch (error) {
  //     // Enhanced error handling
  //     if (error.response) {
  //       // The request was made and the server responded with a status code
  //       throw new Error(
  //         `Etisalat API error: ${error.response.status} - ${JSON.stringify(error.response.data)}`
  //       );
  //     } else if (error.request) {
  //       // The request was made but no response was received
  //       throw new Error('No response received from Etisalat API');
  //     } else {
  //       // Something happened in setting up the request
  //       throw new Error(`Request setup error: ${error.message}`);
  //     }
  //   }
  // }
}
