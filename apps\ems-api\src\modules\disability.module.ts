import { Module } from '@nestjs/common';
import { HttpModule } from 'packages/http';
import { DisabilityController } from '../controllers/disability.controller';
import { DisabilityService } from '../providers/services/disability.service';

@Module({
  // -- not used
  imports: [
    HttpModule.register({
      baseURL: process.env.EMS_API_URL,
      apiTenant: 'ems',
      authTokenParams: {
        oracleCustomToken: true,
      },
    }),
  ],
  controllers: [DisabilityController],
  providers: [DisabilityService],
})
export class DisabilityModule {}
