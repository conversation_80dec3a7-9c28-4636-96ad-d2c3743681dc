import { Module } from '@nestjs/common';
import { HttpModule } from 'packages/http';
import { WebController } from '../controllers/web.controller';
import { WebService } from '../providers/services/web.service';

@Module({
  // -- not used
  imports: [
    HttpModule.register({
      baseURL: process.env.EMS_API_URL,
      apiTenant: 'ems',
      authTokenParams: {
        oracleCustomToken: true,
      },
    }),
  ],
  controllers: [WebController],
  providers: [WebService],
})
export class WebModule {}
