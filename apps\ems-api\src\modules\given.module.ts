import { Module } from '@nestjs/common';
import { HttpModule } from 'packages/http';
import { GivenController } from '../controllers/given.controller';
import { GivenService } from '../providers/services/given.service';

@Module({
  // -- not used
  imports: [
    HttpModule.register({
      baseURL: process.env.EMS_API_URL,
      apiTenant: 'ems',
      authTokenParams: {
        oracleCustomToken: true,
      },
    }),
  ],
  controllers: [GivenController],
  providers: [GivenService],
})
export class GivenModule {}
