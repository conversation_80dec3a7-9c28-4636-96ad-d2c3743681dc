import { Injectable } from '@nestjs/common';
import { HttpService } from 'packages/http';
import { NHServerConfigModel } from '../models/nhserverconfig/nHServerConfig.model';
@Injectable()
export class NhserverconfigService {
  constructor(private readonly http: HttpService) {}

  public async getNhserverById(
    id: number,
    token?: string,
  ): Promise<NHServerConfigModel> {
    // -- not used
    const response = await this.http.get<NHServerConfigModel>(
      `Attachment/nhserver/${id}`,
      { id: id, token: token },
      false,
      { baseURL: 'https://apilooppe.sn.ac.ae/gateway/Parent' }
    );
    return response;
  }

  public async getNhserverconfig(): Promise<NHServerConfigModel> {
    // -- not used
    const response = await this.http.get<NHServerConfigModel>(
      `Nhserverconfig`,
      {},
      false,
      { baseURL: 'https://apilooppe.sn.ac.ae/gateway/Parent' }
    );
    return response;
  }

  public async postNhserverconfig(
    model: NHServerConfigModel,
  ): Promise<NHServerConfigModel> {
    // -- not used
    const response = await this.http.post<NHServerConfigModel>(
      `Nhserverconfig`,
      model,
    );
    return response;
  }

  public async getNhserverconfigById(
    id: number,
    includedetails?: boolean,
  ): Promise<NHServerConfigModel> {
    // -- not used
    const response = await this.http.get<NHServerConfigModel>(
      `Nhserverconfig/${id}`,
      { id: id, includedetails: includedetails },
      false,
    );
    return response;
  }
}
