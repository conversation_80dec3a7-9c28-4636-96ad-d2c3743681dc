import { Module } from '@nestjs/common';
import { HttpModule } from 'packages/http';
import { PersonController } from '../controllers/person.controller';
import { PersonService } from '../providers/services/person.service';

@Module({
  // -- not used
  imports: [
    HttpModule.register({
      baseURL: process.env.EMS_API_URL,
      apiTenant: 'ems',
      authTokenParams: {
        oracleCustomToken: true,
      },
    }),
  ],
  controllers: [PersonController],
  providers: [PersonService],
})
export class PersonModule {}
