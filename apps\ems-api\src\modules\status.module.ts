import { Module } from '@nestjs/common';
import { HttpModule } from 'packages/http';
import { StatusController } from '../controllers/status.controller';
import { StatusService } from '../providers/services/status.service';

@Module({
  // -- not used
  imports: [
    HttpModule.register({
      baseURL: process.env.EMS_API_URL,
      apiTenant: 'ems',
      authTokenParams: {
        oracleCustomToken: true,
      },
    }),
  ],
  controllers: [StatusController],
  providers: [StatusService],
})
export class StatusModule {}
