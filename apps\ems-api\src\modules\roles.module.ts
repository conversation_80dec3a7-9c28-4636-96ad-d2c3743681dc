import { Module } from '@nestjs/common';
import { HttpModule } from 'packages/http';
import { RolesController } from '../controllers/roles.controller';
import { RolesService } from '../providers/services/roles.service';

@Module({
  // -- not used
  imports: [
    HttpModule.register({
      baseURL: process.env.EMS_API_URL,
      apiTenant: 'ems',
      authTokenParams: {
        oracleCustomToken: true,
      },
    }),
  ],
  controllers: [RolesController],
  providers: [RolesService],
})
export class RolesModule {}
