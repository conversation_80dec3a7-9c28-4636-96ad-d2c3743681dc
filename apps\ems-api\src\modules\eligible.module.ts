import { Module } from '@nestjs/common';
import { HttpModule } from 'packages/http';
import { EligibleController } from '../controllers/eligible.controller';
import { EligibleService } from '../providers/services/eligible.service';

@Module({
  // -- not used
  imports: [
    HttpModule.register({
      baseURL: process.env.EMS_API_URL,
      apiTenant: 'ems',
      authTokenParams: {
        oracleCustomToken: true,
      },
    }),
  ],
  controllers: [EligibleController],
  providers: [EligibleService],
})
export class EligibleModule {}
