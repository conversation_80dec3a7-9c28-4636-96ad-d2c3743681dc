import { Module } from '@nestjs/common';
import { HttpModule } from 'packages/http';
import { DependentController } from '../controllers/dependent.controller';
import { DependentService } from '../providers/services/dependent.service';

@Module({
  // -- not used
  imports: [
    HttpModule.register({
      baseURL: process.env.EMS_API_URL,
      apiTenant: 'ems',
      authTokenParams: {
        oracleCustomToken: true,
      },
    }),
  ],
  controllers: [DependentController],
  providers: [DependentService],
})
export class DependentModule {}
