import { Module } from '@nestjs/common';
import { HttpModule } from 'packages/http';
import { EligibilityController } from '../controllers/eligibility.controller';
import { EligibilityService } from '../providers/services/eligibility.service';

@Module({
  // -- not used
  imports: [
    HttpModule.register({
      baseURL: process.env.EMS_API_URL,
      apiTenant: 'ems',
      authTokenParams: {
        oracleCustomToken: true,
      },
    }),
  ],
  controllers: [EligibilityController],
  providers: [EligibilityService],
})
export class EligibilityModule {}
