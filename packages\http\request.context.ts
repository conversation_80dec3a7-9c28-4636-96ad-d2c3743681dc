import { JwtPayload } from 'apps/auth-api/src/auth/jwt-payload.interface';
import { Request } from 'express';
import * as jwt from 'jsonwebtoken';
import { AsyncLocalStorage } from 'node:async_hooks';

export interface RequestStore {
  authorization?: string;
  request?: Request;
}

export const RequestContext = new AsyncLocalStorage<RequestStore>();

export const tokenVerify = (token: string, secret: string): JwtPayload => {
  try {
    return jwt.verify(token, secret) as JwtPayload;
  } catch (err) {
    console.error('Token verification failed:', err);
    throw new Error('Invalid token');
  }
};
