import { Module } from '@nestjs/common';
import { HttpModule } from 'packages/http';
import { HcmController } from '../controllers/hcm.controller';
import { HcmService } from '../providers/services/hcm.service';

@Module({
  // -- not used
  imports: [
    HttpModule.register({
      baseURL: process.env.EMS_API_URL,
      apiTenant: 'ems',
      authTokenParams: {
        oracleCustomToken: true,
      },
    }),
  ],
  controllers: [HcmController],
  providers: [HcmService],
})
export class HcmModule {}
