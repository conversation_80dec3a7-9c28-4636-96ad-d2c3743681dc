import { Module } from '@nestjs/common';
import { HttpModule } from 'packages/http';
import { TrackingController } from '../controllers/tracking.controller';
import { TrackingService } from '../providers/services/tracking.service';

@Module({
  // -- not used
  imports: [
    HttpModule.register({
      baseURL: process.env.EMS_API_URL,
      apiTenant: 'ems',
      authTokenParams: {
        oracleCustomToken: true,
      },
    }),
  ],
  controllers: [TrackingController],
  providers: [TrackingService],
})
export class TrackingModule {}
