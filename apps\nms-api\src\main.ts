import { NestFactory } from '@nestjs/core';

import { ValidationPipe } from '@nestjs/common';
import type { NestExpressApplication } from '@nestjs/platform-express';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { GlobalModule } from './global.module';
// Global unhandled rejection handler
// process.on('unhandledRejection', (reason, promise) => {
//   console.error('Unhandled Promise Rejection:', reason);
//   // You can log the error or take other actions here
// });
async function bootstrap() {
  // const app = await NestFactory.create<NestFastifyApplication>(
  //   AppModule,
  //   new FastifyAdapter({ logger: true })
  // );
  const app = await NestFactory.create<NestExpressApplication>(GlobalModule, {
    snapshot: true,
    abortOnError: false,
  });
  //app.useGlobalInterceptors(app.get(ExternalAuthInterceptor));
  app.useGlobalPipes(
    new ValidationPipe({
      disableErrorMessages: false,
      enableDebugMessages: true,
      skipMissingProperties: true,
      skipUndefinedProperties: true,
    }),
  );

  app.enableCors({
    origin: '*',
    allowedHeaders: '*',
    exposedHeaders: ['AccessToken', 'RefreshToken'],
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
    credentials: true,
    preflightContinue: false,
    optionsSuccessStatus: 204,
  });

  const config = new DocumentBuilder()
    .setTitle('NMS API')
    .setDescription('The NMS API description')
    .setVersion('1.0')
    .addTag('nms')
    .build();
  const documentFactory = () => SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api', app, documentFactory, {
    jsonDocumentUrl: 'swagger/json',
  });
  // const httpAdapter = app.get(HttpAdapterHost);
  // app.useGlobalFilters(new CustomExceptionFilter());
  // app.useGlobalFilters(new CatchEverythingFilter(httpAdapter));
  await app.listen(process.env.port ?? 8080);
}
bootstrap();
