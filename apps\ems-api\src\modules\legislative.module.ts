import { Module } from '@nestjs/common';
import { HttpModule } from 'packages/http';
import { LegislativeController } from '../controllers/legislative.controller';
import { LegislativeService } from '../providers/services/legislative.service';

@Module({
  // -- not used
  imports: [
    HttpModule.register({
      baseURL: process.env.EMS_API_URL,
      apiTenant: 'ems',
      authTokenParams: {
        oracleCustomToken: true,
      },
    }),
  ],
  controllers: [LegislativeController],
  providers: [LegislativeService],
})
export class LegislativeModule {}
