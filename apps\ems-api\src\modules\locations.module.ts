import { Module } from '@nestjs/common';
import { HttpModule } from 'packages/http';
import { LocationsController } from '../controllers/locations.controller';
import { LocationsService } from '../providers/services/locations.service';

@Module({
  // -- not used
  imports: [
    HttpModule.register({
      baseURL: process.env.EMS_API_URL,
      apiTenant: 'ems',
      authTokenParams: {
        oracleCustomToken: true,
      },
    }),
  ],
  controllers: [LocationsController],
  providers: [LocationsService],
})
export class LocationsModule {}
