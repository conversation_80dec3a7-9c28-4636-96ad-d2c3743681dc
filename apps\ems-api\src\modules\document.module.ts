import { Module } from '@nestjs/common';
import { HttpModule } from 'packages/http';
import { DocumentController } from '../controllers/document.controller';
import { DocumentService } from '../providers/services/document.service';
import { TasksService } from '../providers/services/tasks.service';

@Module({
  // -- not used
  imports: [
    HttpModule.register({
      baseURL: process.env.EMS_API_URL,
      apiTenant: 'ems',
      authTokenParams: {
        oracleCustomToken: true,
      },
    }),
  ],
  controllers: [DocumentController],
  providers: [DocumentService, TasksService],
})
export class DocumentModule {}
