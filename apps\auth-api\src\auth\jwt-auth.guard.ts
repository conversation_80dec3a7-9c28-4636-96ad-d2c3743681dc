import {
  ExecutionContext,
  Injectable,
  SetMetadata,
  UnauthorizedException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { AuthGuard } from '@nestjs/passport';

import { JwtService } from '@nestjs/jwt';
import * as fs from 'fs';
import { ExtractJwt } from 'passport-jwt';
import { LockedException } from '../exceptions/locked.exception';
import { JwtPayload } from './jwt-payload.interface';

export const IS_PUBLIC_KEY = 'isPublic';
export const Public = () => SetMetadata(IS_PUBLIC_KEY, true);
export const IS_AUTH_KEY = 'isAuth';
export const Auth = () => SetMetadata(IS_AUTH_KEY, true);
@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
  constructor(
    private reflector: Reflector,
    private jwtService: JwtService,
    // @InjectRepository(UserAuthSession)
    // private userRepository: Repository<UserAuthSession>,
    // @InjectRepository(User)
    // private repository: Repository<User>,
    // private authService: AuthService,
  ) {
    super();
  }

  isExpired = false;

  payload: JwtPayload;

  async canActivate(context: ExecutionContext): Promise<any> {
    // const res = context.switchToHttp().getResponse();
    const req = context.switchToHttp().getRequest();
    console.log(`${req.method}:${req.path}`);
    console.log(JSON.stringify(req.body));
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    const isAuth = this.reflector.getAllAndOverride<boolean>(IS_AUTH_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);
    const tokenInfo = ExtractJwt.fromAuthHeaderAsBearerToken()(
      context.switchToHttp().getRequest(),
    );
    // if (isAuth) {
    //   return true;
    // }
    console.log(`isPublic:${isPublic}`);
    if ((!isPublic || isAuth) && tokenInfo) {
      let verify: any;
      let deletedUsers: any[] = [];
      try {
        verify = await this.jwtService.verify(tokenInfo);
      } catch (error) {
        console.log(`token issue'`);
        console.log(`token Error:${error}`);
        throw new UnauthorizedException('AIHub Token Expired');
      }

      if (fs.existsSync('deletedUsers.json')) {
        try {
          deletedUsers = JSON.parse(
            fs.readFileSync('deletedUsers.json', 'utf8'),
          );
        } catch (error) {
          console.log(`deletedUsers Error`);
          console.log(`deletedUsers Error:${error}`);
          throw new UnauthorizedException('AIHub Token Expired');
        }
        if (deletedUsers.includes(verify?.id)) {
          throw new LockedException('User deleted');
        }
      }
      try {
        const token = tokenInfo;

        //console.log(`WithToken:${token}`);
        if (!token) {
          console.log(`Token not set`);
          throw new UnauthorizedException('Access token is not set');
        }

        // const verify = await this.jwtService.verify(token);
        // if (fs.existsSync('deletedUsers.json')) {
        //   const deletedUsers = JSON.parse(fs.readFileSync('deletedUsers.json', 'utf8'));
        //   if (deletedUsers.includes(verify.id)) {
        //     throw new UnauthorizedException('User deleted');
        //   }
        // }
        // const deletedUsers = JSON.parse(fs.readFileSync('deletedUsers.json', 'utf8'));
        // if (deletedUsers.includes(verify.id)) {
        //   throw new UnauthorizedException('User deleted');
        // }
        return await super.canActivate(context);
      } catch (error) {
        if (error && error.message == 'AIHub Token Expired') {
          console.log(`token issue'`);
          console.log(`token Error:${error}`);
          throw new UnauthorizedException('AIHub Token Expired');
        }

        // if (
        //   (error.name && error.name == 'JsonWebTokenError') ||
        //   (error.expiredAt && error.message == 'jwt expired')
        // ) {
        //   const token = ExtractJwt.fromAuthHeaderAsBearerToken()(
        //     context.switchToHttp().getRequest(),
        //   );
        //   const refreshToken = ExtractJwt.fromHeader('refreshtoken')(
        //     context.switchToHttp().getRequest(),
        //   );
        //   if (!refreshToken) {
        //     console.log(`Refresh token not set`);
        //     throw new UnauthorizedException('Refresh token is not set');
        //   }

        //   const userAuth = await this.userRepository.findOne({
        //     where: {
        //       jwtToken: token,
        //       refreshToken,
        //       iPAddress: req.ip || req.connection?.remoteAddress || 'unknown',
        //     },
        //   });
        //   if (!userAuth) {
        //     console.log(`Refresh token and token does not exist`);
        //     throw new UnauthorizedException(
        //       'Refresh token and token does not exist',
        //     );
        //   }
        //   const entity = await this.repository.findOne({
        //     where: { id: userAuth.userId },
        //   });

        //   if (!entity) {
        //     throw new UnauthorizedException('User not found');
        //   }

        //   this.payload = {
        //     userId: entity.id,
        //     name: entity.name,
        //     email: entity.email || '',
        //     role: 'Employee',
        //     adToken: '',
        //     adExpiresIn: 0,
        //     adRefreshToken: '',
        //     halloToken: '',
        //     halloExpiresIn: 0,
        //     fusionToken: '',
        //     fusionExpiresIn: 0,
        //     nmsToken: '',
        //     nmsExpiresIn: 0,
        //     pmsToken: '',
        //     pmsExpiresIn: 0,
        //     fusionProfile: '',
        //     nmsProfile: '',
        //     pmsProfile: '',
        //     halloProfile: '',
        //     adProfile: '',
        //   };

        //   const response = this.authService.tokenGenerate(this.payload);

        //   const entities = new UserAuthSession();
        //   entities.iPAddress =
        //     req.ip || req.connection?.remoteAddress || 'unknown';
        //   entities.createdDate = new Date().toISOString();

        //   entities.refreshToken = response.refreshToken;
        //   entities.jwtToken = response.accessToken;
        //   entities.userId = entity.id;

        //   res.set({
        //     accessToken: response.accessToken,
        //     refreshToken: response.refreshToken,
        //   });

        //   return await super.canActivate(context);
        // }
      }
    } else if (isPublic) {
      return true;
    }
  }

  handleRequest(err, user) {
    if (err || !user) {
      console.log(`Error:${err}`);
      throw err || new UnauthorizedException();
    } else {
      this.isExpired = false;
      return user;
    }
  }
}
