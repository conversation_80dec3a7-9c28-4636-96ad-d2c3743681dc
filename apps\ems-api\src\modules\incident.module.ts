import { Module } from '@nestjs/common';
import { HttpModule } from 'packages/http';
import { IncidentController } from '../controllers/incident.controller';
import { IncidentService } from '../providers/services/incident.service';

@Module({
  // -- not used
  imports: [
    HttpModule.register({
      baseURL: process.env.EMS_API_URL,
      apiTenant: 'ems',
      authTokenParams: {
        oracleCustomToken: true,
      },
    }),
  ],
  controllers: [IncidentController],
  providers: [IncidentService],
})
export class IncidentModule {}
