import { Module } from '@nestjs/common';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { AppModule as AuthAppModule } from 'apps/auth-api/src/app.module';
import { AppModule as EmsAppModule } from 'apps/ems-api/src/app.module';
import { AppModule as NmsAppModule } from 'apps/nms-api/src/app.module';
import { AppModule as PmsAppModule } from 'apps/pms-api/src/app.module';
import { AppModule as SsrAppModule } from 'apps/ssr-api/src/app.module';
import { ExternalAuthInterceptor } from 'packages/http/externalauth.interceptor';

@Module({
  imports: [
    EmsAppModule,
    SsrAppModule,
    AuthAppModule,
    NmsAppModule,
    PmsAppModule,
  ],
  controllers: [],
  providers: [
    {
      provide: APP_INTERCEPTOR,
      useClass: ExternalAuthInterceptor,
    },
  ],
})
export class AllAppModule {}
