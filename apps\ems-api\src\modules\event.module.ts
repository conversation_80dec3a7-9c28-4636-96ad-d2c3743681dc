import { Module } from '@nestjs/common';
import { HttpModule } from 'packages/http';
import { EventController } from '../controllers/event.controller';
import { EventService } from '../providers/services/event.service';

@Module({
  // -- not used
  imports: [
    HttpModule.register({
      baseURL: process.env.EMS_API_URL,
      apiTenant: 'ems',
      authTokenParams: {
        oracleCustomToken: true,
      },
    }),
  ],
  controllers: [EventController],
  providers: [EventService],
})
export class EventModule {}
