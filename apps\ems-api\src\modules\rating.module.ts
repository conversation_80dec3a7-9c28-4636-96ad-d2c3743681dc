import { Module } from '@nestjs/common';
import { HttpModule } from 'packages/http';
import { RatingController } from '../controllers/rating.controller';
import { RatingService } from '../providers/services/rating.service';

@Module({
  // -- not used
  imports: [
    HttpModule.register({
      baseURL: process.env.EMS_API_URL,
      apiTenant: 'ems',
      authTokenParams: {
        oracleCustomToken: true,
      },
    }),
  ],
  controllers: [RatingController],
  providers: [RatingService],
})
export class RatingModule {}
