import { Module } from '@nestjs/common';
import { HttpModule } from 'packages/http';
import { ProfileController } from '../controllers/profile.controller';
import { ProfileService } from '../providers/services/profile.service';

@Module({
  // -- not used
  imports: [
    HttpModule.register({
      baseURL: process.env.EMS_API_URL,
      apiTenant: 'ems',
      authTokenParams: {
        oracleCustomToken: true,
      },
    }),
  ],
  controllers: [ProfileController],
  providers: [ProfileService],
})
export class ProfileModule {}
