import { Module } from '@nestjs/common';
import { HttpModule } from 'packages/http';
import { DevelopmentController } from '../controllers/development.controller';
import { DevelopmentService } from '../providers/services/development.service';

@Module({
  // -- not used
  imports: [
    HttpModule.register({
      baseURL: process.env.EMS_API_URL,
      apiTenant: 'ems',
      authTokenParams: {
        oracleCustomToken: true,
      },
    }),
  ],
  controllers: [DevelopmentController],
  providers: [DevelopmentService],
})
export class DevelopmentModule {}
