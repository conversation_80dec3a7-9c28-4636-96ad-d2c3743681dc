import { Module } from '@nestjs/common';
import { HttpModule } from 'packages/http';
import { ReviewController } from '../controllers/review.controller';
import { ReviewService } from '../providers/services/review.service';

@Module({
  // -- not used
  imports: [
    HttpModule.register({
      baseURL: process.env.EMS_API_URL,
      apiTenant: 'ems',
      authTokenParams: {
        oracleCustomToken: true,
      },
    }),
  ],
  controllers: [ReviewController],
  providers: [ReviewService],
})
export class ReviewModule {}
