import { Module } from '@nestjs/common';
import { HttpModule } from 'packages/http';
import { ResponsibilityController } from '../controllers/responsibility.controller';
import { ResponsibilityService } from '../providers/services/responsibility.service';

@Module({
  // -- not used
  imports: [
    HttpModule.register({
      baseURL: process.env.EMS_API_URL,
      apiTenant: 'ems',
      authTokenParams: {
        oracleCustomToken: true,
      },
    }),
  ],
  controllers: [ResponsibilityController],
  providers: [ResponsibilityService],
})
export class ResponsibilityModule {}
