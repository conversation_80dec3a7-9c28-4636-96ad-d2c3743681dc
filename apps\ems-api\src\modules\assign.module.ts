import { Module } from '@nestjs/common';
import { HttpModule } from 'packages/http';
import { AssignController } from '../controllers/assign.controller';
import { AssignService } from '../providers/services/assign.service';

@Module({
  // -- not used
  imports: [
    HttpModule.register({
      baseURL: process.env.EMS_API_URL,
      apiTenant: 'ems',
      authTokenParams: {
        oracleCustomToken: true,
      },
    }),
  ],
  controllers: [AssignController],
  providers: [AssignService],
})
export class AssignModule {}
