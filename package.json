{"name": "sea-project", "version": "0.0.5", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"apps/**/*.ts\" \"packages/**/*.ts\"", "dev": "nest start sea-project --watch --debug --env-file .env.development", "dev:employees": "nest start ems-api --watch --debug --env-file .env.development", "dev:nurseries": "nest start nms-api --watch --debug --env-file .env.development", "lint": "eslint \"{src,apps,packages,test}/**/*.ts\" --fix", "generate": "node main-default.js"}, "dependencies": {"@nestjs/axios": "^4.0.0", "@nestjs/common": "^11.0.11", "@nestjs/config": "^4.0.1", "@nestjs/core": "^11.0.11", "@nestjs/devtools-integration": "^0.2.0", "@nestjs/jwt": "^11.0.0", "@nestjs/mapped-types": "^2.1.0", "@nestjs/microservices": "^11.0.11", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.6", "@nestjs/platform-fastify": "^11.0.6", "@nestjs/platform-socket.io": "^11.0.11", "@nestjs/schedule": "^5.0.1", "@nestjs/swagger": "^11.0.6", "@nestjs/typeorm": "^11.0.0", "@nestjs/websockets": "^11.0.11", "@types/multer": "^1.4.12", "aws-sdk": "^2.1692.0", "axios": "1.8.3", "base64url": "^3.0.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "crypto": "^1.0.1", "dotenv": "^16.4.7", "express-session": "^1.18.1", "jwt-decode": "^4.0.0", "multer": "^1.4.5-lts.2", "mysql2": "^3.13.0", "node-forge": "^1.3.1", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "typeorm": "^0.3.21"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/express": "^5.0.0", "@types/express-session": "^1.18.1", "@types/jest": "^29.5.14", "@types/node": "^22.10.7", "@types/supertest": "^6.0.2", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "eslint-plugin-unused-imports": "^4.1.4", "globals": "^15.14.0", "jest": "^29.7.0", "openai": "^4.0.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "./coverage", "testEnvironment": "node", "roots": ["<rootDir>/apps/"]}}