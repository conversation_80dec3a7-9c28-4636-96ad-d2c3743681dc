import { HttpModule } from 'packages/http';
const url = process.env.NMS_API_URL;
const userName = process.env.NMS_API_CLIENT_ID;
const password = process.env.NMS_API_CLIENT_SECRET;
const basicAuthenticationToken = Buffer.from(
  `${userName}:${password}`,
).toString('base64');

const defaultTestingUserId = process.env.NMS_API_USER_ID;

export const DefaultHttpModules = () => {
  return HttpModule.register({
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Basic ${basicAuthenticationToken}`,
    },
    postHeaders: {
      'Content-Type': 'application/json',
    },
    baseURL: process.env.NMS_API_URL,
    authTokenUrl: url + '/User/authenticate/AuthenticateViaIdentifier',
    authTokenResponseKey: 'JwtToken',
    authTokenContentType: 'application/json',
    apiTenant: 'nms',
    authTokenParams: {
      userId: defaultTestingUserId,
      // Password: password,
    },
  });
};
