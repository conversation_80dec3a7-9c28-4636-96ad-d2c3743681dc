import { sign } from 'jsonwebtoken';
//import forge from 'node-forge';
import { asn1, md, pki } from 'node-forge';
const privateKey = `***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`;

const pubKey = `-----BEGIN CERTIFICATE-----
MIIDazCCAlOgAwIBAgIUDbrnYK3XQGyBOaOc9QmX8vjXxkIwDQYJKoZIhvcNAQEL
BQAwRTELMAkGA1UEBhMCQUUxEzARBgNVBAgMClNvbWUtU3RhdGUxITAfBgNVBAoM
GEludGVybmV0IFdpZGdpdHMgUHR5IEx0ZDAeFw0yNTAzMDQwNTU1MzZaFw0yNjAz
MDQwNTU1MzZaMEUxCzAJBgNVBAYTAkFFMRMwEQYDVQQIDApTb21lLVN0YXRlMSEw
HwYDVQQKDBhJbnRlcm5ldCBXaWRnaXRzIFB0eSBMdGQwggEiMA0GCSqGSIb3DQEB
AQUAA4IBDwAwggEKAoIBAQCq8oLiWL64IhP8EZJrOomdh4uA2vGCYh2HLUOlZ69v
gXabLSsGTjvjkyW0tsLd/sNRL8vj44pth2TJH2SgjPK6HxZWYVEoK4cKLGeCU0Dh
hjje5dBKXZ/TKTAJkE2576yuIlOm37qDkqwS7/cOLbojPiLmwWq4sWqZcQCfGnOK
NExzX66dnruorD5gje6DzfA2em/Fxb63TDWzTM8K/gMGPWEWTaH402tMKbMaVeCq
UxcGPhZhS81yDHq19HhzIjWbw4t+nnIv/2/3f1+TqUMg/yP9gfRFbmKiS0DXPQU5
Wb7KwSL7vycPS1dtJXfLuQusI1XuryqUbdN52+iR187xAgMBAAGjUzBRMB0GA1Ud
DgQWBBRDjXAE2J+105B9t7dQ6qONLG5DdTAfBgNVHSMEGDAWgBRDjXAE2J+105B9
t7dQ6qONLG5DdTAPBgNVHRMBAf8EBTADAQH/MA0GCSqGSIb3DQEBCwUAA4IBAQA8
1Z3DaJgmR0Tm6IxbyWR21TltVD81xR4NxD4OCp7k+zv4QDIVekjA3+0IDJ0/MINH
XIyJFxOljdyxkHQjQFP/F/ngCCsefx95wIVcpiLxQSoTfuNTWDPjrp5c2knMwygh
bwtYhz0gne6x3H23icdlVkdjGUN+pERFqjGXoVoAuiNi9RxakC7cGZqttHCX5vP9
XR3JGQGI1p0o2blNQlqsC55GWVZ/pCbPcN7GTE6Mjaw/aaqbWdxACErz/9/6x6Tp
vqNSQf8KE+/N7gZgRYgwVYMSq2BbvcPcAkX0OEnvivUnJ7GDDC4rVZnoh35IJo4P
SJ7srKK7M2Rhsy7GRuAr
-----END CERTIFICATE-----`;

export function generateOracleJWT(username: string): string | null {
  const iss = process.env.EMS_API_JWT_ISSUER; // JWT issuer - iss attribute
  const aud = process.env.EMS_API_JWT_AUDIENCE; // JWT audience - aud attribute

  //   // Relative file paths
  //   const privateKeyPath = path.resolve(
  //     __dirname,
  //     '../oraclekeys/dev/private_key.pem',
  //   );
  //   const publicKeyPath = path.resolve(__dirname, '../oraclekeys/dev/pub.pem');

  try {
    // Read the private key directly in PEM format
    // const privateKeyPem = fs.readFileSync(privateKey, 'utf8');

    // // Read the public key certificate (PEM format)
    // const publicKeyPem = fs.readFileSync(pubKey, 'utf8');

    // Generate the SHA-1 thumbprint for x5t
    const publicKey = pki.certificateFromPem(pubKey);
    const certAsn1 = pki.certificateToAsn1(publicKey);
    const certDer = asn1.toDer(certAsn1).getBytes();
    const sha1 = md.sha1.create().update(certDer).digest().getBytes();
    const thumbprint = Buffer.from(sha1, 'binary').toString('base64');

    const now = Math.floor(Date.now() / 1000);
    const iat = now - 2 * 24 * 60 * 60; // Issued 2 days back
    const exp = iat + 4 * 24 * 60 * 60; // Expiry 4 days from iat (total 2 days valid)

    // JWT payload
    const payload = {
      iss,
      sub: username,
      aud,
      iat,
      exp,
    };

    // JWT header
    const header = {
      alg: 'RS256',
      typ: 'JWT',
      x5t: thumbprint,
    };

    // Sign the JWT using RS256
    const token = sign(payload, privateKey, {
      algorithm: 'RS256',
      header,
    });

    return token;
  } catch (error) {
    console.error('Error generating JWT:', error);
    return null;
  }
}

// // erp.integration
// // AShuhail
// // MAl Masri
// const username = 'AShuhail';

// // Example usage
// const jwtToken = generateOracleJWT(username);
// console.log('Generated JWT:', jwtToken);
