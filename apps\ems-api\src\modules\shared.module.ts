import { Module } from '@nestjs/common';
import { HttpModule } from 'packages/http';
import { SharedController } from '../controllers/shared.controller';
import { SharedService } from '../providers/services/shared.service';

@Module({
  // -- not used
  imports: [
    HttpModule.register({
      baseURL: process.env.EMS_API_URL,
      apiTenant: 'ems',
      authTokenParams: {
        oracleCustomToken: true,
      },
    }),
  ],
  controllers: [SharedController],
  providers: [SharedService],
})
export class SharedModule {}
