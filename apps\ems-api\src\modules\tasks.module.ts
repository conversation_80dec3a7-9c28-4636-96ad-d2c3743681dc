import { Module } from '@nestjs/common';
import { HttpModule } from 'packages/http';
import { TasksController } from '../controllers/tasks.controller';
import { TasksService } from '../providers/services/tasks.service';
const basicAuth = `Basic ${Buffer.from(`${process.env.EMS_API_USER3}:${process.env.EMS_API_PASSWORD3}`).toString('base64')}`;
@Module({
  // -- not used
  imports: [
    HttpModule.register({
      baseURL: process.env.EMS_API_URL2,
      headers: {
        Authorization: basicAuth,
      },
    }),
  ],
  controllers: [TasksController],
  providers: [TasksService],
})
export class TasksModule {}
