import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';

// import { jwtConstants } from '../constants';
const PassportJwtStrategy = PassportStrategy(Strategy);
//const global = '';

@Injectable()
export class JwtStrategy extends PassportJwtStrategy {
  constructor() {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: true,
      secretOrKey: process.env.TOKEN_SECRET || 'defaultSecretKey',
    });
  }

  validate(payload: any): any {
    return {
      clientId: payload.clientId,
      isCriteriaFilled: payload.isCriteriaFilled,
      userId: payload.id,
      email: payload.email,
      isSubscribed: payload.isSubscribed,
      username: payload.username,
      roleId: payload.roleId,
    };
  }
}
