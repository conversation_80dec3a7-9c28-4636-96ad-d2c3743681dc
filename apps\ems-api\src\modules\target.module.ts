import { Module } from '@nestjs/common';
import { HttpModule } from 'packages/http';
import { TargetController } from '../controllers/target.controller';
import { TargetService } from '../providers/services/target.service';

@Module({
  // -- not used
  imports: [
    HttpModule.register({
      baseURL: process.env.EMS_API_URL,
      apiTenant: 'ems',
      authTokenParams: {
        oracleCustomToken: true,
      },
    }),
  ],
  controllers: [TargetController],
  providers: [TargetService],
})
export class TargetModule {}
