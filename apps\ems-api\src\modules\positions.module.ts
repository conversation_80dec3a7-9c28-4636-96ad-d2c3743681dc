import { Module } from '@nestjs/common';
import { HttpModule } from 'packages/http';
import { PositionsController } from '../controllers/positions.controller';
import { PositionsService } from '../providers/services/positions.service';

@Module({
  // -- not used
  imports: [
    HttpModule.register({
      baseURL: process.env.EMS_API_URL,
      apiTenant: 'ems',
      authTokenParams: {
        oracleCustomToken: true,
      },
    }),
  ],
  controllers: [PositionsController],
  providers: [PositionsService],
})
export class PositionsModule {}
