import { HttpModule } from 'packages/http';
const url = process.env.PMS_API_URL;
const userName = process.env.PMS_API_CLIENT_ID;
const password = process.env.PMS_API_CLIENT_SECRET;
const basicAuthenticationToken = Buffer.from(
  `${userName}:${password}`,
).toString('base64');

const defaultTestingParentId = process.env.PMS_API_PARENT_ID;

export const DefaultHttpModules = () => {
  return HttpModule.register({
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Basic ${basicAuthenticationToken}`,
    },
    postHeaders: {
      'Content-Type': 'application/json',
    },
    baseURL: process.env.NO_PARENT_PMS_API_URL,
    authTokenUrl: url + '/authenticate/AuthenticateParentViaIdentifier',
    authTokenResponseKey: 'JwtToken',
    authTokenContentType: 'application/json',
    apiTenant: 'pms',
    authTokenParams: {
      userId: defaultTestingParentId,
      // Password: password,
    },
  });
};
